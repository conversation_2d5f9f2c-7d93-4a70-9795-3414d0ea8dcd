<template>
  <footer class="app-footer">
    <div class="container">
      <div class="footer-content">
        <!-- Company Info -->
        <div class="footer-section">
          <div class="footer-logo">
            <div class="logo-icon">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                <rect x="2" y="6" width="28" height="20" rx="2" stroke="currentColor" stroke-width="2"/>
                <path d="M2 8L16 18L30 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <span class="logo-text">邮箱售卖</span>
          </div>
          <p class="footer-description">
            专业的教育和企业邮箱服务提供商，为您提供高质量、稳定可靠的邮箱解决方案。
          </p>
          <div class="social-links">
            <a href="#" class="social-link" aria-label="微信">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18z"/>
              </svg>
            </a>
            <a href="#" class="social-link" aria-label="QQ">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-1.5-1.5L10 14l2-2 2 2 1.5 1.5L14 17l-2-2-2 2z"/>
              </svg>
            </a>
            <a href="#" class="social-link" aria-label="邮箱">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                <polyline points="22,6 12,13 2,6"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="footer-section">
          <h3 class="footer-title">快速链接</h3>
          <ul class="footer-links">
            <li><router-link to="/" class="footer-link">首页</router-link></li>
            <li><router-link to="/products" class="footer-link">产品列表</router-link></li>
            <li><router-link to="/about" class="footer-link">关于我们</router-link></li>
            <li><a href="#" class="footer-link">帮助中心</a></li>
            <li><a href="#" class="footer-link">联系我们</a></li>
          </ul>
        </div>

        <!-- Product Categories -->
        <div class="footer-section">
          <h3 class="footer-title">产品分类</h3>
          <ul class="footer-links">
            <li><a href="#" class="footer-link">教育邮箱</a></li>
            <li><a href="#" class="footer-link">企业邮箱</a></li>
            <li><a href="#" class="footer-link">个人邮箱</a></li>
            <li><a href="#" class="footer-link">Gmail 邮箱</a></li>
            <li><a href="#" class="footer-link">Outlook 邮箱</a></li>
          </ul>
        </div>

        <!-- Support -->
        <div class="footer-section">
          <h3 class="footer-title">客户支持</h3>
          <ul class="footer-links">
            <li><a href="#" class="footer-link">常见问题</a></li>
            <li><a href="#" class="footer-link">使用指南</a></li>
            <li><a href="#" class="footer-link">技术支持</a></li>
            <li><a href="#" class="footer-link">退款政策</a></li>
            <li><a href="#" class="footer-link">隐私政策</a></li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div class="footer-section">
          <h3 class="footer-title">联系方式</h3>
          <div class="contact-info">
            <div class="contact-item">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                <polyline points="22,6 12,13 2,6"/>
              </svg>
              <span><EMAIL></span>
            </div>
            <div class="contact-item">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
              </svg>
              <span>400-123-4567</span>
            </div>
            <div class="contact-item">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="10" r="3"/>
                <path d="M12 21.7C17.3 17 20 13 20 10a8 8 0 1 0-16 0c0 3 2.7 7 8 11.7z"/>
              </svg>
              <span>中国 · 北京</span>
            </div>
            <div class="contact-item">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
              <span>7×24小时在线服务</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Bottom -->
      <div class="footer-bottom">
        <div class="footer-bottom-content">
          <div class="copyright">
            <p>&copy; 2024 邮箱售卖平台. 保留所有权利.</p>
          </div>
          <div class="footer-bottom-links">
            <a href="#" class="footer-bottom-link">服务条款</a>
            <a href="#" class="footer-bottom-link">隐私政策</a>
            <a href="#" class="footer-bottom-link">Cookie政策</a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
// No reactive state needed for footer
</script>

<style scoped>
.app-footer {
  background-color: var(--color-neutral-black);
  color: var(--color-neutral-white);
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  padding: var(--spacing-2xl) 0;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* Logo Section */
.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  font-size: 1.25rem;
  margin-bottom: var(--spacing-sm);
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--color-secondary-surface);
  border: var(--border-width) solid var(--color-neutral-white);
  border-radius: var(--border-radius);
  color: var(--color-neutral-black);
}

.footer-description {
  color: #ccc;
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

/* Social Links */
.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: transparent;
  border: var(--border-width) solid var(--color-neutral-white);
  border-radius: var(--border-radius);
  color: var(--color-neutral-white);
  transition: all 0.2s ease;
}

.social-link:hover {
  background-color: var(--color-secondary-surface);
  color: var(--color-neutral-black);
  transform: translate(1px, 1px);
}

/* Footer Sections */
.footer-title {
  font-size: 1.125rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  color: var(--color-neutral-white);
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-link {
  color: #ccc;
  text-decoration: none;
  transition: all 0.2s ease;
  padding: var(--spacing-xs) 0;
}

.footer-link:hover {
  color: var(--color-secondary-surface);
  transform: translateX(4px);
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: #ccc;
}

.contact-item svg {
  flex-shrink: 0;
  color: var(--color-secondary-surface);
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid #333;
  padding: var(--spacing-lg) 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.copyright {
  color: #999;
  font-size: 0.875rem;
}

.footer-bottom-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-bottom-link {
  color: #999;
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.2s ease;
}

.footer-bottom-link:hover {
  color: var(--color-secondary-surface);
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-bottom-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .social-links {
    justify-content: center;
  }
  
  .footer-bottom-links {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
</style>
