{"name": "vite-plugin-vue-devtools", "type": "module", "version": "7.7.7", "description": "A vite plugin for Vue DevTools", "author": "webfansplz", "license": "MIT", "homepage": "https://github.com/vuejs/devtools#readme", "repository": {"directory": "packages/vite", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "keywords": ["vue-devtools", "vite-plugin", "vite-plugin-vue-devtools", "dx"], "exports": {".": {"types": "./dist/vite.d.ts", "import": "./dist/vite.mjs", "require": "./dist/vite.cjs"}, "./*": "./*"}, "main": "dist/vite.cjs", "module": "dist/vite.mjs", "types": "dist/vite.d.ts", "files": ["*.d.ts", "./src/overlay.js", "./src/overlay/**", "client", "dist", "overlay"], "engines": {"node": ">=v14.21.3"}, "peerDependencies": {"vite": "^3.1.0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0"}, "dependencies": {"execa": "^9.5.2", "sirv": "^3.0.1", "vite-plugin-inspect": "0.8.9", "vite-plugin-vue-inspector": "^5.3.1", "@vue/devtools-core": "^7.7.7", "@vue/devtools-shared": "^7.7.7", "@vue/devtools-kit": "^7.7.7"}, "devDependencies": {"@types/node": "^22.13.14", "fast-glob": "^3.3.3", "image-meta": "^0.2.1", "pathe": "^2.0.3"}, "scripts": {"build": "unbuild", "stub": "tsup --watch"}}