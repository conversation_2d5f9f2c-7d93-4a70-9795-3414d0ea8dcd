# 邮箱售卖平台 - 前端项目

基于 Vue 3 + Vite 构建的现代化邮箱售卖平台前端应用，采用 Neo-Brutalist Tech 设计风格，参考 Cursor 的界面设计理念。

## 🚀 项目特性

- **现代化技术栈**: Vue 3 + Vite + Pinia + Vue Router
- **Neo-Brutalist 设计**: 高对比度、粗轮廓、硬阴影的设计风格
- **响应式设计**: 完美适配桌面端和移动端
- **状态管理**: 使用 Pinia 进行全局状态管理
- **路由守卫**: 完整的认证和权限控制
- **API 接口层**: 为后端集成预留完整的接口层
- **组件化开发**: 高度可复用的组件设计

## 📁 项目结构

```
src/
├── api/                    # API 接口层
│   └── index.js           # 统一的 API 调用接口
├── assets/                # 静态资源
│   ├── base.css          # 基础样式重置
│   └── main.css          # Neo-Brutalist 设计系统样式
├── components/            # 可复用组件
│   └── layout/           # 布局组件
│       ├── AppHeader.vue # 顶部导航栏
│       └── AppFooter.vue # 底部信息栏
├── router/               # 路由配置
│   └── index.js         # 路由定义和守卫
├── stores/              # Pinia 状态管理
│   ├── auth.js         # 用户认证状态
│   ├── cart.js         # 购物车状态
│   └── products.js     # 产品数据状态
├── views/               # 页面组件
│   ├── auth/           # 认证相关页面
│   │   ├── LoginView.vue    # 登录页面
│   │   └── RegisterView.vue # 注册页面
│   ├── HomeView.vue         # 首页
│   ├── ProductsView.vue     # 产品列表页
│   ├── CartView.vue         # 购物车页面
│   ├── DashboardView.vue    # 用户仪表板
│   ├── ProfileView.vue      # 个人资料页
│   ├── AboutView.vue        # 关于我们页面
│   └── NotFoundView.vue     # 404 页面
├── App.vue              # 根组件
└── main.js             # 应用入口
```

## 🛠️ 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖

```bash
npm install
```

### 开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 🎨 设计系统

### 颜色规范

```css
/* 主要颜色 */
--color-primary-bg: #F9F900;        /* 充满活力的亮黄色背景 */
--color-secondary-surface: #43C4B2;  /* 中等饱和度的青绿色 */
--color-accent-highlight: #FF00FF;   /* 鲜艳的品红色强调色 */
--color-neutral-white: #FFFFFF;      /* 纯白色 */
--color-neutral-black: #000000;      /* 纯黑色 */
```

### 字体规范

- **字体族**: 'Roboto Mono', monospace
- **字重**: 400 (regular), 500 (medium), 700 (bold)
- **大小写**: 按钮文本使用全大写，标题使用句首大写

### 组件样式

- **边框**: 统一使用 2px 实线黑色边框
- **圆角**: 8px 统一圆角
- **阴影**: 3px 偏移的硬阴影效果
- **间距**: 基于 4px 的间距系统

## 🔧 核心功能模块

### 1. 用户认证系统

**文件位置**: `src/stores/auth.js`, `src/views/auth/`

**功能特性**:
- 用户登录/注册
- JWT Token 管理
- 路由守卫
- 用户状态持久化

**API 接口**:
```javascript
// 登录
authAPI.login({ email, password })

// 注册
authAPI.register({ name, email, password })

// 获取用户信息
authAPI.getUser()

// 更新用户资料
authAPI.updateProfile(profileData)
```

### 2. 产品管理系统

**文件位置**: `src/stores/products.js`, `src/views/ProductsView.vue`

**功能特性**:
- 产品列表展示
- 分类筛选
- 价格范围筛选
- 搜索功能
- 排序功能

**API 接口**:
```javascript
// 获取产品列表
productsAPI.getProducts(filters)

// 获取产品详情
productsAPI.getProduct(productId)

// 获取分类列表
productsAPI.getCategories()
```

### 3. 购物车系统

**文件位置**: `src/stores/cart.js`, `src/views/CartView.vue`

**功能特性**:
- 添加/删除商品
- 数量调整
- 本地存储持久化
- 结算功能

**API 接口**:
```javascript
// 创建订单
ordersAPI.createOrder(orderData)

// 获取订单列表
ordersAPI.getOrders()
```

## 🔌 API 接口规范

### 基础配置

```javascript
// API 基础 URL
const API_BASE_URL = 'http://localhost:3000/api'

// 请求头配置
headers: {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${token}`
}
```

### 响应格式

```javascript
// 成功响应
{
  "success": true,
  "data": { ... },
  "message": "操作成功"
}

// 错误响应
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE"
}
```

### 主要接口端点

#### 认证接口
- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `POST /auth/logout` - 用户登出
- `GET /auth/me` - 获取当前用户信息
- `PUT /auth/profile` - 更新用户资料

#### 产品接口
- `GET /products` - 获取产品列表
- `GET /products/:id` - 获取产品详情
- `GET /products/categories` - 获取产品分类

#### 订单接口
- `POST /orders` - 创建订单
- `GET /orders` - 获取用户订单列表
- `GET /orders/:id` - 获取订单详情
- `DELETE /orders/:id` - 取消订单

## 🎯 状态管理

### Auth Store (认证状态)

```javascript
// 状态
{
  user: null,           // 用户信息
  token: null,          // 认证令牌
  isLoading: false,     // 加载状态
  error: null           // 错误信息
}

// 主要方法
login(credentials)      // 登录
register(userData)      // 注册
logout()               // 登出
updateProfile(data)    // 更新资料
```

### Products Store (产品状态)

```javascript
// 状态
{
  products: [],         // 产品列表
  categories: [],       // 分类列表
  filters: {},          // 筛选条件
  isLoading: false      // 加载状态
}

// 主要方法
fetchProducts()        // 获取产品
updateFilters(filters) // 更新筛选
resetFilters()         // 重置筛选
```

### Cart Store (购物车状态)

```javascript
// 状态
{
  items: [],            // 购物车商品
  isLoading: false      // 加载状态
}

// 主要方法
addItem(product)       // 添加商品
removeItem(productId)  // 删除商品
updateQuantity(id, qty) // 更新数量
clearCart()            // 清空购物车
```

## 🚦 路由配置

### 路由结构

```javascript
{
  path: '/',
  name: 'home',
  component: HomeView,
  meta: { requiresAuth: false }
}
```

### 路由守卫

- **认证检查**: 检查用户登录状态
- **权限控制**: 根据用户角色控制访问
- **重定向**: 未登录用户重定向到登录页

## 📱 响应式设计

### 断点设置

- **桌面端**: >= 1024px
- **平板端**: 768px - 1023px
- **移动端**: < 768px

### 适配策略

- 使用 CSS Grid 和 Flexbox 布局
- 移动端优先的设计理念
- 触摸友好的交互设计

## 🔄 后端集成指南

### 1. 环境变量配置

创建 `.env` 文件：

```env
VITE_API_BASE_URL=http://your-backend-url/api
VITE_APP_NAME=邮箱售卖平台
```

### 2. API 客户端配置

修改 `src/api/index.js` 中的 `getMockResponse` 方法，替换为实际的 HTTP 请求：

```javascript
async request(endpoint, options = {}) {
  const url = `${this.baseURL}${endpoint}`
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...this.getAuthHeaders(),
      ...options.headers,
    },
  })

  if (!response.ok) {
    throw new ApiError(`HTTP ${response.status}`, response.status)
  }

  return response.json()
}
```

### 3. 错误处理

- 统一的错误处理机制
- 用户友好的错误提示
- 网络错误重试机制

## 🧪 测试建议

### 单元测试
- 使用 Vitest 进行组件测试
- 测试 Store 的状态变更
- 测试 API 调用逻辑

### 集成测试
- 测试用户完整的操作流程
- 测试路由跳转和权限控制
- 测试表单提交和验证

## 📈 性能优化

### 代码分割
- 路由级别的懒加载
- 组件按需导入
- 第三方库的按需引入

### 资源优化
- 图片压缩和格式优化
- CSS 和 JS 文件压缩
- 启用 Gzip 压缩

## 🔒 安全考虑

### 前端安全
- XSS 防护
- CSRF 防护
- 敏感信息不在前端存储

### 认证安全
- JWT Token 安全存储
- Token 过期处理
- 安全的登出机制

## 📞 技术支持

如有技术问题，请联系开发团队或查看项目文档。

---

**项目版本**: 1.0.0
**最后更新**: 2024年1月
**维护团队**: 前端开发组
