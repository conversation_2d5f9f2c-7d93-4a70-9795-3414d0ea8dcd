/* Base CSS for Neo-Brutalist Design System */
/* This file contains minimal base styles that work with our design system */

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  min-height: 100vh;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Remove default button styles */
button {
  border: none;
  background: none;
  font: inherit;
  cursor: pointer;
}

/* Remove default input styles */
input, textarea, select {
  font: inherit;
  border: none;
  outline: none;
  background: none;
}

/* Remove default link styles */
a {
  text-decoration: none;
  color: inherit;
}

/* Remove default list styles */
ul, ol {
  list-style: none;
}

/* Image responsive by default */
img {
  max-width: 100%;
  height: auto;
  display: block;
}
