{"name": "vite-plugin-vue-inspector", "version": "5.3.2", "description": "jump to local IDE source code while click the element of browser automatically.", "author": "webfansplz", "license": "MIT", "homepage": "https://github.com/webfansplz/vite-plugin-vue-inspector#readme", "repository": {"type": "git", "url": "git+https://github.com/webfansplz/vite-plugin-vue-inspector.git"}, "bugs": {"url": "https://github.com/webfansplz/vite-plugin-vue-inspector/issues"}, "keywords": ["vue", "vite", "vscode", "vite-plugin", "inspector", "debug"], "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.mjs"}, "./src/*": "./src/*"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "src/load.js", "src/Overlay.vue"], "peerDependencies": {"vite": "^3.0.0-0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0"}, "dependencies": {"@babel/core": "^7.23.0", "@babel/plugin-proposal-decorators": "^7.23.0", "@babel/plugin-syntax-import-attributes": "^7.22.5", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-typescript": "^7.22.15", "@vue/babel-plugin-jsx": "^1.1.5", "@vue/compiler-dom": "^3.3.4", "kolorist": "^1.8.0", "magic-string": "^0.30.4"}, "devDependencies": {"@types/babel__core": "^7.20.2", "unplugin": "^1.5.0"}, "scripts": {"lint": "eslint --fix --ext .js,.ts,.vue .", "dev": "tsup --watch", "build": "tsup"}}