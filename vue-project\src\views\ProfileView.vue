<template>
  <div class="profile-page">
    <div class="container">
      <!-- Page Header -->
      <div class="page-header">
        <h1>个人资料</h1>
        <p>管理您的账户信息和偏好设置</p>
      </div>

      <div class="profile-content">
        <!-- Profile Form -->
        <div class="profile-form-section">
          <div class="window">
            <div class="window-header">
              <div class="window-title">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                  <circle cx="12" cy="7" r="4"/>
                </svg>
                <span>基本信息</span>
              </div>
            </div>
            <div class="form-content">
              <form @submit.prevent="handleUpdateProfile" class="profile-form">
                <!-- Avatar Section -->
                <div class="avatar-section">
                  <div class="avatar-display">
                    <div class="avatar">
                      <span>{{ userInitials }}</span>
                    </div>
                    <button type="button" class="btn btn-outline btn-sm" @click="handleAvatarUpload">
                      更换头像
                    </button>
                  </div>
                </div>

                <!-- Name Field -->
                <div class="form-group">
                  <label for="name" class="form-label">用户名</label>
                  <input
                    id="name"
                    type="text"
                    class="input"
                    :class="{ 'input-error': errors.name }"
                    v-model="form.name"
                    placeholder="请输入您的用户名"
                    required
                  >
                  <div v-if="errors.name" class="error-message">{{ errors.name }}</div>
                </div>

                <!-- Email Field -->
                <div class="form-group">
                  <label for="email" class="form-label">邮箱地址</label>
                  <input
                    id="email"
                    type="email"
                    class="input"
                    :class="{ 'input-error': errors.email }"
                    v-model="form.email"
                    placeholder="请输入您的邮箱地址"
                    required
                  >
                  <div v-if="errors.email" class="error-message">{{ errors.email }}</div>
                </div>

                <!-- Phone Field -->
                <div class="form-group">
                  <label for="phone" class="form-label">手机号码</label>
                  <input
                    id="phone"
                    type="tel"
                    class="input"
                    v-model="form.phone"
                    placeholder="请输入您的手机号码"
                  >
                </div>

                <!-- Bio Field -->
                <div class="form-group">
                  <label for="bio" class="form-label">个人简介</label>
                  <textarea
                    id="bio"
                    class="input textarea"
                    v-model="form.bio"
                    placeholder="简单介绍一下自己..."
                    rows="4"
                  ></textarea>
                </div>

                <!-- Error Message -->
                <div v-if="authError" class="error-banner">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                  </svg>
                  <span>{{ authError }}</span>
                </div>

                <!-- Success Message -->
                <div v-if="successMessage" class="success-banner">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                    <polyline points="22,4 12,14.01 9,11.01"/>
                  </svg>
                  <span>{{ successMessage }}</span>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="btn btn-primary" :disabled="isLoading">
                  <svg v-if="isLoading" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="loading-spinner">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56"/>
                  </svg>
                  <span>{{ isLoading ? '保存中...' : '保存更改' }}</span>
                </button>
              </form>
            </div>
          </div>
        </div>

        <!-- Account Settings -->
        <div class="settings-section">
          <div class="window">
            <div class="window-header">
              <div class="window-title">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="3"/>
                  <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                </svg>
                <span>账户设置</span>
              </div>
            </div>
            <div class="settings-content">
              <!-- Change Password -->
              <div class="setting-item">
                <div class="setting-info">
                  <h3>修改密码</h3>
                  <p>定期更改密码以保护您的账户安全</p>
                </div>
                <button class="btn btn-outline" @click="showChangePassword = true">
                  修改密码
                </button>
              </div>

              <!-- Email Notifications -->
              <div class="setting-item">
                <div class="setting-info">
                  <h3>邮件通知</h3>
                  <p>接收订单更新和产品推荐邮件</p>
                </div>
                <label class="toggle-switch">
                  <input type="checkbox" v-model="settings.emailNotifications">
                  <span class="toggle-slider"></span>
                </label>
              </div>

              <!-- Marketing Emails -->
              <div class="setting-item">
                <div class="setting-info">
                  <h3>营销邮件</h3>
                  <p>接收特别优惠和新产品信息</p>
                </div>
                <label class="toggle-switch">
                  <input type="checkbox" v-model="settings.marketingEmails">
                  <span class="toggle-slider"></span>
                </label>
              </div>

              <!-- Account Deletion -->
              <div class="setting-item danger">
                <div class="setting-info">
                  <h3>删除账户</h3>
                  <p>永久删除您的账户和所有相关数据</p>
                </div>
                <button class="btn btn-danger" @click="handleDeleteAccount">
                  删除账户
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Change Password Modal -->
      <div v-if="showChangePassword" class="modal-overlay" @click="showChangePassword = false">
        <div class="modal" @click.stop>
          <div class="modal-header">
            <h3>修改密码</h3>
            <button class="modal-close" @click="showChangePassword = false">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          </div>
          <div class="modal-content">
            <form @submit.prevent="handleChangePassword" class="password-form">
              <div class="form-group">
                <label for="currentPassword" class="form-label">当前密码</label>
                <input
                  id="currentPassword"
                  type="password"
                  class="input"
                  v-model="passwordForm.currentPassword"
                  required
                >
              </div>
              <div class="form-group">
                <label for="newPassword" class="form-label">新密码</label>
                <input
                  id="newPassword"
                  type="password"
                  class="input"
                  v-model="passwordForm.newPassword"
                  required
                >
              </div>
              <div class="form-group">
                <label for="confirmNewPassword" class="form-label">确认新密码</label>
                <input
                  id="confirmNewPassword"
                  type="password"
                  class="input"
                  v-model="passwordForm.confirmNewPassword"
                  required
                >
              </div>
              <div class="modal-actions">
                <button type="button" class="btn btn-outline" @click="showChangePassword = false">
                  取消
                </button>
                <button type="submit" class="btn btn-primary">
                  确认修改
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '../stores/auth'

const authStore = useAuthStore()

// Reactive state
const form = ref({
  name: '',
  email: '',
  phone: '',
  bio: ''
})

const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmNewPassword: ''
})

const settings = ref({
  emailNotifications: true,
  marketingEmails: false
})

const errors = ref({})
const successMessage = ref('')
const showChangePassword = ref(false)

// Computed properties
const user = computed(() => authStore.user)
const isLoading = computed(() => authStore.isLoading)
const authError = computed(() => authStore.error)

const userInitials = computed(() => {
  if (!user.value) return 'U'
  const name = user.value.name || user.value.email
  return name.charAt(0).toUpperCase()
})

// Methods
const loadUserData = () => {
  if (user.value) {
    form.value = {
      name: user.value.name || '',
      email: user.value.email || '',
      phone: user.value.phone || '',
      bio: user.value.bio || ''
    }
  }
}

const validateForm = () => {
  errors.value = {}
  
  if (!form.value.name?.trim()) {
    errors.value.name = '请输入用户名'
  }
  
  if (!form.value.email) {
    errors.value.email = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
    errors.value.email = '请输入有效的邮箱地址'
  }
  
  return Object.keys(errors.value).length === 0
}

const handleUpdateProfile = async () => {
  if (!validateForm()) return
  
  authStore.clearError()
  successMessage.value = ''
  
  const result = await authStore.updateProfile({
    name: form.value.name.trim(),
    email: form.value.email,
    phone: form.value.phone,
    bio: form.value.bio
  })
  
  if (result.success) {
    successMessage.value = '个人资料更新成功！'
    setTimeout(() => {
      successMessage.value = ''
    }, 3000)
  }
}

const handleChangePassword = async () => {
  if (passwordForm.value.newPassword !== passwordForm.value.confirmNewPassword) {
    alert('新密码和确认密码不一致')
    return
  }
  
  // TODO: Implement password change API
  alert('密码修改功能即将推出！')
  showChangePassword.value = false
  
  // Reset form
  passwordForm.value = {
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: ''
  }
}

const handleAvatarUpload = () => {
  // TODO: Implement avatar upload
  alert('头像上传功能即将推出！')
}

const handleDeleteAccount = () => {
  if (confirm('确定要删除账户吗？此操作不可撤销！')) {
    // TODO: Implement account deletion
    alert('账户删除功能即将推出！')
  }
}

// Watch for user changes
watch(user, loadUserData, { immediate: true })

// Lifecycle
onMounted(() => {
  loadUserData()
})
</script>

<style scoped>
.profile-page {
  padding: var(--spacing-2xl) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.page-header h1 {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

.page-header p {
  color: #666;
  font-size: 1.125rem;
}

.profile-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
}

/* Profile Form */
.form-content {
  padding: var(--spacing-lg);
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.avatar-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: var(--color-secondary-surface);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: 50%;
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form-label {
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
  text-transform: uppercase;
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

.error-banner,
.success-banner {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border: var(--border-width) solid;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
}

.error-banner {
  background-color: #fef2f2;
  border-color: #dc2626;
  color: #dc2626;
}

.success-banner {
  background-color: #f0fdf4;
  border-color: #16a34a;
  color: #16a34a;
}

/* Settings Section */
.settings-content {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--color-neutral-white);
  border: 1px solid #e5e7eb;
  border-radius: var(--border-radius);
}

.setting-item.danger {
  border-color: #dc2626;
  background-color: #fef2f2;
}

.setting-info h3 {
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.setting-info p {
  font-size: 0.875rem;
  color: #666;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: 24px;
  transition: 0.2s;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: var(--color-neutral-white);
  border: 1px solid var(--color-neutral-black);
  border-radius: 50%;
  transition: 0.2s;
}

input:checked + .toggle-slider {
  background-color: var(--color-secondary-surface);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Buttons */
.btn-danger {
  background-color: #dc2626;
  color: var(--color-neutral-white);
  border-color: #dc2626;
}

.btn-danger:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--color-secondary-surface);
  border-bottom: var(--border-width) solid var(--color-neutral-black);
}

.modal-header h3 {
  font-size: 1.125rem;
  font-weight: var(--font-weight-bold);
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
}

.modal-content {
  padding: var(--spacing-lg);
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .profile-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .modal {
    margin: var(--spacing-md);
    max-width: none;
  }
}
</style>
