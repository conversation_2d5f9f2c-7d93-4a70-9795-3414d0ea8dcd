<template>
  <div class="auth-page">
    <div class="auth-container">
      <!-- Auth Window -->
      <div class="auth-window">
        <!-- Window Header -->
        <div class="window-header">
          <div class="window-title">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="8.5" cy="7" r="4"/>
              <line x1="20" y1="8" x2="20" y2="14"/>
              <line x1="23" y1="11" x2="17" y2="11"/>
            </svg>
            <span>用户注册</span>
          </div>
          <div class="window-controls">
            <div class="window-control minimize"></div>
            <div class="window-control maximize"></div>
            <div class="window-control close"></div>
          </div>
        </div>

        <!-- Window Content -->
        <div class="window-content">
          <!-- Logo Section -->
          <div class="auth-logo">
            <div class="logo-icon">
              <svg width="48" height="48" viewBox="0 0 32 32" fill="none">
                <rect x="2" y="6" width="28" height="20" rx="2" stroke="currentColor" stroke-width="2"/>
                <path d="M2 8L16 18L30 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <h1 class="auth-title">邮箱售卖平台</h1>
            <p class="auth-subtitle">创建您的账户</p>
          </div>

          <!-- Register Form -->
          <form @submit.prevent="handleRegister" class="auth-form">
            <!-- Name Field -->
            <div class="form-group">
              <label for="name" class="form-label">用户名</label>
              <input
                id="name"
                type="text"
                class="input"
                :class="{ 'input-error': errors.name }"
                v-model="form.name"
                placeholder="请输入您的用户名"
                autocomplete="name"
              >
              <div v-if="errors.name" class="error-message">{{ errors.name }}</div>
            </div>

            <!-- Email Field -->
            <div class="form-group">
              <label for="email" class="form-label">邮箱地址</label>
              <input
                id="email"
                type="email"
                class="input"
                :class="{ 'input-error': errors.email }"
                v-model="form.email"
                placeholder="请输入您的邮箱地址"
                required
                autocomplete="email"
              >
              <div v-if="errors.email" class="error-message">{{ errors.email }}</div>
            </div>

            <!-- Password Field -->
            <div class="form-group">
              <label for="password" class="form-label">密码</label>
              <div class="password-input-wrapper">
                <input
                  id="password"
                  :type="showPassword ? 'text' : 'password'"
                  class="input"
                  :class="{ 'input-error': errors.password }"
                  v-model="form.password"
                  placeholder="请输入密码（至少6个字符）"
                  required
                  autocomplete="new-password"
                >
                <button
                  type="button"
                  class="password-toggle"
                  @click="togglePassword"
                  :aria-label="showPassword ? '隐藏密码' : '显示密码'"
                >
                  <svg v-if="showPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                  </svg>
                  <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                  </svg>
                </button>
              </div>
              <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
              <div class="password-strength">
                <div class="strength-bar">
                  <div class="strength-fill" :class="passwordStrengthClass" :style="{ width: passwordStrengthWidth }"></div>
                </div>
                <span class="strength-text">{{ passwordStrengthText }}</span>
              </div>
            </div>

            <!-- Confirm Password Field -->
            <div class="form-group">
              <label for="confirmPassword" class="form-label">确认密码</label>
              <div class="password-input-wrapper">
                <input
                  id="confirmPassword"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  class="input"
                  :class="{ 'input-error': errors.confirmPassword }"
                  v-model="form.confirmPassword"
                  placeholder="请再次输入密码"
                  required
                  autocomplete="new-password"
                >
                <button
                  type="button"
                  class="password-toggle"
                  @click="toggleConfirmPassword"
                  :aria-label="showConfirmPassword ? '隐藏密码' : '显示密码'"
                >
                  <svg v-if="showConfirmPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                  </svg>
                  <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                  </svg>
                </button>
              </div>
              <div v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</div>
            </div>

            <!-- Terms and Privacy -->
            <div class="form-group">
              <label class="checkbox-wrapper">
                <input 
                  type="checkbox" 
                  v-model="form.agreeToTerms" 
                  class="checkbox"
                  :class="{ 'input-error': errors.agreeToTerms }"
                  required
                >
                <span class="checkbox-label">
                  我同意 <a href="#" class="terms-link">服务条款</a> 和 <a href="#" class="terms-link">隐私政策</a>
                </span>
              </label>
              <div v-if="errors.agreeToTerms" class="error-message">{{ errors.agreeToTerms }}</div>
            </div>

            <!-- Marketing Emails -->
            <div class="form-group">
              <label class="checkbox-wrapper">
                <input type="checkbox" v-model="form.subscribeNewsletter" class="checkbox">
                <span class="checkbox-label">接收产品更新和营销邮件</span>
              </label>
            </div>

            <!-- Error Message -->
            <div v-if="authError" class="error-banner">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="15" y1="9" x2="9" y2="15"/>
                <line x1="9" y1="9" x2="15" y2="15"/>
              </svg>
              <span>{{ authError }}</span>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn btn-primary btn-full" :disabled="isLoading">
              <svg v-if="isLoading" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="loading-spinner">
                <path d="M21 12a9 9 0 1 1-6.219-8.56"/>
              </svg>
              <span>{{ isLoading ? '注册中...' : '创建账户' }}</span>
            </button>
          </form>

          <!-- Divider -->
          <div class="auth-divider">
            <span>或</span>
          </div>

          <!-- Social Register -->
          <div class="social-login">
            <button class="btn btn-outline social-btn" @click="handleSocialRegister('google')">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>Google 注册</span>
            </button>
            <button class="btn btn-outline social-btn" @click="handleSocialRegister('github')">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              <span>GitHub 注册</span>
            </button>
          </div>

          <!-- Login Link -->
          <div class="auth-footer">
            <p>已有账户？ <router-link to="/login" class="auth-link">立即登录</router-link></p>
          </div>
        </div>
      </div>

      <!-- Decorative Graphics -->
      <div class="decorative-graphics">
        <div class="graphic-plus">
          <svg width="100" height="100" viewBox="0 0 100 100" fill="none">
            <line x1="50" y1="10" x2="50" y2="90" stroke="currentColor" stroke-width="3"/>
            <line x1="10" y1="50" x2="90" y2="50" stroke="currentColor" stroke-width="3"/>
          </svg>
        </div>
        <div class="graphic-squares">
          <div class="square"></div>
          <div class="square"></div>
          <div class="square"></div>
          <div class="square"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// Reactive state
const form = ref({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeToTerms: false,
  subscribeNewsletter: false
})

const errors = ref({})
const showPassword = ref(false)
const showConfirmPassword = ref(false)

// Computed properties
const isLoading = computed(() => authStore.isLoading)
const authError = computed(() => authStore.error)

const passwordStrength = computed(() => {
  const password = form.value.password
  if (!password) return 0
  
  let strength = 0
  if (password.length >= 6) strength += 1
  if (password.length >= 8) strength += 1
  if (/[A-Z]/.test(password)) strength += 1
  if (/[a-z]/.test(password)) strength += 1
  if (/[0-9]/.test(password)) strength += 1
  if (/[^A-Za-z0-9]/.test(password)) strength += 1
  
  return Math.min(strength, 4)
})

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value
  if (strength === 0) return ''
  if (strength === 1) return '弱'
  if (strength === 2) return '一般'
  if (strength === 3) return '强'
  return '很强'
})

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 1) return 'weak'
  if (strength === 2) return 'fair'
  if (strength === 3) return 'good'
  return 'strong'
})

const passwordStrengthWidth = computed(() => {
  return `${(passwordStrength.value / 4) * 100}%`
})

// Methods
const validateForm = () => {
  errors.value = {}
  
  if (!form.value.name?.trim()) {
    errors.value.name = '请输入用户名'
  } else if (form.value.name.trim().length < 2) {
    errors.value.name = '用户名至少需要2个字符'
  }
  
  if (!form.value.email) {
    errors.value.email = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
    errors.value.email = '请输入有效的邮箱地址'
  }
  
  if (!form.value.password) {
    errors.value.password = '请输入密码'
  } else if (form.value.password.length < 6) {
    errors.value.password = '密码至少需要6个字符'
  }
  
  if (!form.value.confirmPassword) {
    errors.value.confirmPassword = '请确认密码'
  } else if (form.value.password !== form.value.confirmPassword) {
    errors.value.confirmPassword = '两次输入的密码不一致'
  }
  
  if (!form.value.agreeToTerms) {
    errors.value.agreeToTerms = '请同意服务条款和隐私政策'
  }
  
  return Object.keys(errors.value).length === 0
}

const handleRegister = async () => {
  if (!validateForm()) return
  
  authStore.clearError()
  
  const result = await authStore.register({
    name: form.value.name.trim(),
    email: form.value.email,
    password: form.value.password,
    subscribeNewsletter: form.value.subscribeNewsletter
  })
  
  if (result.success) {
    router.push('/dashboard')
  }
}

const handleSocialRegister = (provider) => {
  // TODO: Implement social registration
  console.log(`Social register with ${provider}`)
}

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

// Watch for password changes to clear confirm password error
watch(() => form.value.password, () => {
  if (errors.value.confirmPassword && form.value.password === form.value.confirmPassword) {
    delete errors.value.confirmPassword
  }
})

watch(() => form.value.confirmPassword, () => {
  if (errors.value.confirmPassword && form.value.password === form.value.confirmPassword) {
    delete errors.value.confirmPassword
  }
})

// Lifecycle
onMounted(() => {
  authStore.clearError()
})
</script>

<style scoped>
/* Register-specific styles - inherits base auth styles from main.css */

.password-strength {
  margin-top: var(--spacing-sm);
}

.strength-bar {
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: var(--spacing-xs);
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background-color: #dc2626;
}

.strength-fill.fair {
  background-color: #f59e0b;
}

.strength-fill.good {
  background-color: #10b981;
}

.strength-fill.strong {
  background-color: #059669;
}

.strength-text {
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
}

.strength-text:empty {
  display: none;
}

.terms-link {
  color: var(--color-neutral-black);
  text-decoration: underline;
  font-weight: var(--font-weight-medium);
}

.terms-link:hover {
  color: var(--color-secondary-surface);
}

/* Register-specific decorative graphics */
.graphic-plus {
  position: absolute;
  top: 15%;
  left: 10%;
  color: var(--color-neutral-black);
  opacity: 0.2;
}

.graphic-squares {
  position: absolute;
  bottom: 15%;
  right: 10%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-sm);
}

.square {
  width: 20px;
  height: 20px;
  background-color: var(--color-neutral-black);
  opacity: 0.3;
  border-radius: 2px;
}

@media (max-width: 768px) {
  .auth-window {
    max-width: 100%;
  }
  
  .window-content {
    padding: var(--spacing-lg);
  }
}
</style>
