/**
 * API 接口层
 * 统一管理所有API调用，为后端集成做准备
 */

// API 基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'
const API_TIMEOUT = 10000

/**
 * HTTP 请求工具类
 */
class ApiClient {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL
    this.timeout = API_TIMEOUT
  }

  /**
   * 获取认证头
   */
  getAuthHeaders() {
    const token = localStorage.getItem('auth_token')
    return token ? { Authorization: `Bearer ${token}` } : {}
  }

  /**
   * 通用请求方法
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    const config = {
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...this.getAuthHeaders(),
        ...options.headers,
      },
      ...options,
    }

    try {
      // 在实际环境中，这里会使用 fetch 或 axios
      // 目前返回模拟数据用于开发
      console.log(`API Request: ${options.method || 'GET'} ${url}`, config)
      
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))
      
      // 根据不同的端点返回模拟数据
      return this.getMockResponse(endpoint, options)
    } catch (error) {
      console.error('API Request failed:', error)
      throw new ApiError(error.message || 'Network request failed', error.status || 500)
    }
  }

  /**
   * GET 请求
   */
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const url = queryString ? `${endpoint}?${queryString}` : endpoint
    return this.request(url, { method: 'GET' })
  }

  /**
   * POST 请求
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  /**
   * PUT 请求
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  /**
   * DELETE 请求
   */
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' })
  }

  /**
   * 模拟响应数据（开发阶段使用）
   * 在实际项目中，这个方法会被移除
   */
  getMockResponse(endpoint, options) {
    const method = options.method || 'GET'
    
    // 认证相关接口
    if (endpoint === '/auth/login' && method === 'POST') {
      return {
        success: true,
        data: {
          user: {
            id: 1,
            email: JSON.parse(options.body).email,
            name: JSON.parse(options.body).email.split('@')[0],
            role: 'user',
            avatar: null,
            createdAt: new Date().toISOString()
          },
          token: 'mock_jwt_token_' + Date.now()
        }
      }
    }

    if (endpoint === '/auth/register' && method === 'POST') {
      const userData = JSON.parse(options.body)
      return {
        success: true,
        data: {
          user: {
            id: Date.now(),
            email: userData.email,
            name: userData.name || userData.email.split('@')[0],
            role: 'user',
            avatar: null,
            createdAt: new Date().toISOString()
          },
          token: 'mock_jwt_token_' + Date.now()
        }
      }
    }

    if (endpoint === '/auth/logout' && method === 'POST') {
      return { success: true, message: 'Logged out successfully' }
    }

    if (endpoint === '/auth/me' && method === 'GET') {
      return {
        success: true,
        data: {
          id: 1,
          email: '<EMAIL>',
          name: 'Test User',
          role: 'user',
          avatar: null,
          createdAt: new Date().toISOString()
        }
      }
    }

    // 产品相关接口
    if (endpoint === '/products' && method === 'GET') {
      return {
        success: true,
        data: [
          {
            id: 1,
            name: 'Gmail 教育邮箱',
            type: 'gmail',
            category: 'education',
            price: 15.99,
            description: '高质量Gmail教育邮箱，支持Google Drive无限存储',
            features: ['无限存储', 'Google服务', '教育优惠'],
            availability: 'in_stock',
            popularity: 95,
            image: '/images/gmail-edu.png'
          },
          // ... 其他产品数据
        ]
      }
    }

    // 订单相关接口
    if (endpoint === '/orders' && method === 'POST') {
      const orderData = JSON.parse(options.body)
      return {
        success: true,
        data: {
          id: 'ORDER_' + Date.now(),
          items: orderData.items,
          total: orderData.total,
          status: 'pending',
          createdAt: new Date().toISOString(),
          paymentMethod: orderData.payment?.method || 'unknown'
        }
      }
    }

    // 默认响应
    return {
      success: false,
      error: 'Endpoint not implemented in mock'
    }
  }
}

/**
 * API 错误类
 */
class ApiError extends Error {
  constructor(message, status = 500, data = null) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.data = data
  }
}

// 创建 API 客户端实例
const apiClient = new ApiClient()

/**
 * 认证相关 API
 */
export const authAPI = {
  /**
   * 用户登录
   */
  async login(credentials) {
    const response = await apiClient.post('/auth/login', credentials)
    if (!response.success) {
      throw new ApiError(response.error || 'Login failed', 401)
    }
    return response.data
  },

  /**
   * 用户注册
   */
  async register(userData) {
    const response = await apiClient.post('/auth/register', userData)
    if (!response.success) {
      throw new ApiError(response.error || 'Registration failed', 400)
    }
    return response.data
  },

  /**
   * 用户登出
   */
  async logout() {
    const response = await apiClient.post('/auth/logout')
    if (!response.success) {
      throw new ApiError(response.error || 'Logout failed', 500)
    }
    return response
  },

  /**
   * 获取当前用户信息
   */
  async getUser() {
    const response = await apiClient.get('/auth/me')
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to get user info', 401)
    }
    return response.data
  },

  /**
   * 更新用户资料
   */
  async updateProfile(profileData) {
    const response = await apiClient.put('/auth/profile', profileData)
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to update profile', 400)
    }
    return response.data
  },

  /**
   * 修改密码
   */
  async changePassword(passwordData) {
    const response = await apiClient.put('/auth/password', passwordData)
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to change password', 400)
    }
    return response
  }
}

/**
 * 产品相关 API
 */
export const productsAPI = {
  /**
   * 获取产品列表
   */
  async getProducts(filters = {}) {
    const response = await apiClient.get('/products', filters)
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to get products', 500)
    }
    return response.data
  },

  /**
   * 获取产品详情
   */
  async getProduct(productId) {
    const response = await apiClient.get(`/products/${productId}`)
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to get product', 404)
    }
    return response.data
  },

  /**
   * 获取产品分类
   */
  async getCategories() {
    const response = await apiClient.get('/products/categories')
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to get categories', 500)
    }
    return response.data
  }
}

/**
 * 订单相关 API
 */
export const ordersAPI = {
  /**
   * 创建订单
   */
  async createOrder(orderData) {
    const response = await apiClient.post('/orders', orderData)
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to create order', 400)
    }
    return response.data
  },

  /**
   * 获取用户订单列表
   */
  async getOrders(params = {}) {
    const response = await apiClient.get('/orders', params)
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to get orders', 500)
    }
    return response.data
  },

  /**
   * 获取订单详情
   */
  async getOrder(orderId) {
    const response = await apiClient.get(`/orders/${orderId}`)
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to get order', 404)
    }
    return response.data
  },

  /**
   * 取消订单
   */
  async cancelOrder(orderId) {
    const response = await apiClient.delete(`/orders/${orderId}`)
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to cancel order', 400)
    }
    return response
  }
}

/**
 * 支付相关 API
 */
export const paymentAPI = {
  /**
   * 创建支付
   */
  async createPayment(paymentData) {
    const response = await apiClient.post('/payments', paymentData)
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to create payment', 400)
    }
    return response.data
  },

  /**
   * 验证支付状态
   */
  async verifyPayment(paymentId) {
    const response = await apiClient.get(`/payments/${paymentId}/verify`)
    if (!response.success) {
      throw new ApiError(response.error || 'Failed to verify payment', 400)
    }
    return response.data
  }
}

// 导出 API 错误类和客户端
export { ApiError, apiClient }

// 默认导出所有 API
export default {
  auth: authAPI,
  products: productsAPI,
  orders: ordersAPI,
  payment: paymentAPI,
  client: apiClient,
  ApiError
}
