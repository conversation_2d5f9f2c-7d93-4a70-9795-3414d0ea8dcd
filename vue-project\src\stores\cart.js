import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useCartStore = defineStore('cart', () => {
  // State
  const items = ref([])
  const isLoading = ref(false)
  const error = ref(null)

  // Getters
  const itemCount = computed(() => items.value.reduce((total, item) => total + item.quantity, 0))
  const totalPrice = computed(() => items.value.reduce((total, item) => total + (item.price * item.quantity), 0))
  const isEmpty = computed(() => items.value.length === 0)

  // Actions
  const addItem = (product, quantity = 1) => {
    const existingItem = items.value.find(item => item.id === product.id)
    
    if (existingItem) {
      existingItem.quantity += quantity
    } else {
      items.value.push({
        id: product.id,
        name: product.name,
        price: product.price,
        type: product.type,
        description: product.description,
        quantity: quantity,
        addedAt: new Date().toISOString()
      })
    }
    
    // Save to localStorage
    saveToLocalStorage()
  }

  const removeItem = (productId) => {
    const index = items.value.findIndex(item => item.id === productId)
    if (index > -1) {
      items.value.splice(index, 1)
      saveToLocalStorage()
    }
  }

  const updateQuantity = (productId, quantity) => {
    const item = items.value.find(item => item.id === productId)
    if (item) {
      if (quantity <= 0) {
        removeItem(productId)
      } else {
        item.quantity = quantity
        saveToLocalStorage()
      }
    }
  }

  const clearCart = () => {
    items.value = []
    saveToLocalStorage()
  }

  const checkout = async (paymentData) => {
    isLoading.value = true
    error.value = null
    
    try {
      // TODO: Replace with actual API call
      // const response = await orderAPI.createOrder({
      //   items: items.value,
      //   payment: paymentData,
      //   total: totalPrice.value
      // })
      
      // Mock checkout for development
      const mockOrder = {
        id: 'ORDER_' + Date.now(),
        items: [...items.value],
        total: totalPrice.value,
        status: 'pending',
        createdAt: new Date().toISOString(),
        paymentMethod: paymentData.method
      }
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Clear cart after successful checkout
      clearCart()
      
      return { success: true, order: mockOrder }
    } catch (err) {
      error.value = err.message || '结算失败'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const saveToLocalStorage = () => {
    try {
      localStorage.setItem('cart_items', JSON.stringify(items.value))
    } catch (err) {
      console.error('Failed to save cart to localStorage:', err)
    }
  }

  const loadFromLocalStorage = () => {
    try {
      const savedItems = localStorage.getItem('cart_items')
      if (savedItems) {
        items.value = JSON.parse(savedItems)
      }
    } catch (err) {
      console.error('Failed to load cart from localStorage:', err)
      items.value = []
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Initialize cart from localStorage
  const initializeCart = () => {
    loadFromLocalStorage()
  }

  return {
    // State
    items,
    isLoading,
    error,
    
    // Getters
    itemCount,
    totalPrice,
    isEmpty,
    
    // Actions
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    checkout,
    clearError,
    initializeCart
  }
})
