/* Neo-Brutalist Tech Design System */
@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;700&display=swap');

/* CSS Variables for Neo-Brutalist Design System */
:root {
  /* Color Palette */
  --color-primary-bg: #F9F900;
  --color-secondary-surface: #43C4B2;
  --color-accent-highlight: #FF00FF;
  --color-neutral-white: #FFFFFF;
  --color-neutral-black: #000000;

  /* Typography */
  --font-family-mono: 'Roboto Mono', monospace;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* Border & Shadow */
  --border-width: 2px;
  --border-radius: 8px;
  --shadow-offset: 3px;

  /* Layout */
  --container-max-width: 1200px;
  --header-height: 64px;
}

/* Global Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-mono);
  font-weight: var(--font-weight-regular);
  color: var(--color-neutral-black);
  background-color: var(--color-primary-bg);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-black);
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-lg);
}

h2 {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-md);
}

p {
  margin-bottom: var(--spacing-md);
}

/* Button Base Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-xl);
  font-family: var(--font-family-mono);
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
  text-transform: uppercase;
  text-decoration: none;
  color: var(--color-neutral-black);
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.btn:hover {
  transform: translate(1px, 1px);
  box-shadow: 2px 2px 0 var(--color-neutral-black);
}

.btn:active {
  transform: translate(var(--shadow-offset), var(--shadow-offset));
  box-shadow: none;
}

.btn-primary {
  background-color: var(--color-secondary-surface);
}

.btn-accent {
  background-color: var(--color-accent-highlight);
  color: var(--color-neutral-white);
}

/* Input Base Styles */
.input {
  width: 100%;
  padding: var(--spacing-md);
  font-family: var(--font-family-mono);
  font-size: 1rem;
  color: var(--color-neutral-black);
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  outline: none;
  transition: all 0.2s ease;
}

.input:focus {
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
}

.input::placeholder {
  color: #666;
  opacity: 0.7;
}

/* Card Base Styles */
.card {
  background-color: var(--color-secondary-surface);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-neutral-white);
  border-bottom: var(--border-width) solid var(--color-neutral-black);
}

.card-body {
  padding: var(--spacing-lg);
}

/* Window/Container Styles */
.window {
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
  overflow: hidden;
}

.window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-secondary-surface);
  border-bottom: var(--border-width) solid var(--color-neutral-black);
}

.window-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.window-control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid var(--color-neutral-black);
}

.window-control.close { background-color: #ff5f56; }
.window-control.minimize { background-color: #ffbd2e; }
.window-control.maximize { background-color: #27ca3f; }

/* Utility Classes */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.text-center { text-align: center; }
.text-uppercase { text-transform: uppercase; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

/* Auth Pages Common Styles */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  background-color: var(--color-primary-bg);
  position: relative;
  overflow: hidden;
}

.auth-container {
  position: relative;
  z-index: 2;
}

.auth-window {
  width: 100%;
  max-width: 800px;
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
  overflow: hidden;
}

.window-content {
  padding: var(--spacing-2xl);
}

.auth-logo {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.logo-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: var(--color-secondary-surface);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
}

.auth-title {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
}

.auth-subtitle {
  color: #666;
  font-size: 0.875rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form-label {
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
  text-transform: uppercase;
}

.password-input-wrapper {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: var(--spacing-xs);
}

.input-error {
  border-color: #dc2626;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 #dc2626;
}

.error-message {
  color: #dc2626;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: 2px;
}

.checkbox-label {
  font-size: 0.875rem;
}

.forgot-password {
  color: var(--color-neutral-black);
  font-size: 0.875rem;
  text-decoration: underline;
}

.error-banner {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: #fef2f2;
  border: var(--border-width) solid #dc2626;
  border-radius: var(--border-radius);
  color: #dc2626;
  font-size: 0.875rem;
}

.success-banner {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: #f0fdf4;
  border: var(--border-width) solid #16a34a;
  border-radius: var(--border-radius);
  color: #16a34a;
  font-size: 0.875rem;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.auth-divider {
  text-align: center;
  position: relative;
  margin: var(--spacing-xl) 0;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--color-neutral-black);
}

.auth-divider span {
  background-color: var(--color-neutral-white);
  padding: 0 var(--spacing-md);
  font-size: 0.875rem;
  color: #666;
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.social-btn {
  justify-content: center;
  gap: var(--spacing-sm);
}

.auth-footer {
  text-align: center;
  margin-top: var(--spacing-xl);
  font-size: 0.875rem;
}

.auth-link {
  color: var(--color-neutral-black);
  font-weight: var(--font-weight-bold);
  text-decoration: underline;
}

/* Decorative Graphics for Auth Pages */
.decorative-graphics {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.graphic-grid {
  position: absolute;
  top: 10%;
  right: 10%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
}

.grid-dot {
  width: 8px;
  height: 8px;
  background-color: var(--color-neutral-black);
  border-radius: 50%;
  opacity: 0.3;
}

.graphic-wireframe {
  position: absolute;
  bottom: 10%;
  left: 5%;
  color: var(--color-neutral-black);
  opacity: 0.2;
}

/* Auth Page Responsive Design */
@media (max-width: 768px) {
  .auth-page {
    padding: var(--spacing-md);
  }

  .window-content {
    padding: var(--spacing-xl);
  }

  .decorative-graphics {
    display: none;
  }
}

/* Auth Pages Common Styles */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  background-color: var(--color-primary-bg);
  position: relative;
  overflow: hidden;
}

.auth-container {
  position: relative;
  z-index: 2;
}

.auth-window {
  width: 100%;
  max-width: 480px;
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
  overflow: hidden;
}

.window-content {
  padding: var(--spacing-2xl);
}

.auth-logo {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.logo-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: var(--color-secondary-surface);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
}

.auth-title {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
}

.auth-subtitle {
  color: #666;
  font-size: 0.875rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form-label {
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
  text-transform: uppercase;
}

.password-input-wrapper {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: var(--spacing-xs);
}

.input-error {
  border-color: #dc2626;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 #dc2626;
}

.error-message {
  color: #dc2626;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: 2px;
}

.checkbox-label {
  font-size: 0.875rem;
}

.forgot-password {
  color: var(--color-neutral-black);
  font-size: 0.875rem;
  text-decoration: underline;
}

.error-banner {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: #fef2f2;
  border: var(--border-width) solid #dc2626;
  border-radius: var(--border-radius);
  color: #dc2626;
  font-size: 0.875rem;
}

.success-banner {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: #f0fdf4;
  border: var(--border-width) solid #16a34a;
  border-radius: var(--border-radius);
  color: #16a34a;
  font-size: 0.875rem;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.auth-divider {
  text-align: center;
  position: relative;
  margin: var(--spacing-xl) 0;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--color-neutral-black);
}

.auth-divider span {
  background-color: var(--color-neutral-white);
  padding: 0 var(--spacing-md);
  font-size: 0.875rem;
  color: #666;
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.social-btn {
  justify-content: center;
  gap: var(--spacing-sm);
}

.auth-footer {
  text-align: center;
  margin-top: var(--spacing-xl);
  font-size: 0.875rem;
}

.auth-link {
  color: var(--color-neutral-black);
  font-weight: var(--font-weight-bold);
  text-decoration: underline;
}
