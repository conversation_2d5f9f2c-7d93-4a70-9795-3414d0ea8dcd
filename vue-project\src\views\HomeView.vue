<script setup>
import { ref, computed, onMounted } from 'vue'
import { useProductsStore } from '../stores/products'

const productsStore = useProductsStore()

// Computed properties
const featuredProducts = computed(() => {
  return productsStore.products
    .filter(product => product.popularity > 85)
    .slice(0, 3)
})

onMounted(async () => {
  await productsStore.initializeProducts()
})
</script>

<template>
  <div class="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">专业邮箱服务平台</h1>
            <p class="hero-subtitle">
              提供高质量的教育邮箱和企业邮箱解决方案<br>
              Gmail、Outlook、Yahoo 等多种选择
            </p>
            <div class="hero-actions">
              <router-link to="/products" class="btn btn-primary btn-large">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="9" cy="21" r="1"/>
                  <circle cx="20" cy="21" r="1"/>
                  <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
                </svg>
                <span>立即购买</span>
              </router-link>
              <router-link to="/about" class="btn btn-outline btn-large">
                了解更多
              </router-link>
            </div>
          </div>
          <div class="hero-graphic">
            <div class="window hero-window">
              <div class="window-header">
                <div class="window-title">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="2" y="6" width="20" height="12" rx="2"/>
                    <path d="M2 8l10 6 10-6"/>
                  </svg>
                  <span>邮箱服务</span>
                </div>
                <div class="window-controls">
                  <div class="window-control minimize"></div>
                  <div class="window-control maximize"></div>
                  <div class="window-control close"></div>
                </div>
              </div>
              <div class="window-content">
                <div class="email-preview">
                  <div class="email-item">
                    <div class="email-type gmail">Gmail</div>
                    <div class="email-info">
                      <div class="email-name">教育邮箱</div>
                      <div class="email-price">¥15.99</div>
                    </div>
                  </div>
                  <div class="email-item">
                    <div class="email-type outlook">Outlook</div>
                    <div class="email-info">
                      <div class="email-name">企业邮箱</div>
                      <div class="email-price">¥22.99</div>
                    </div>
                  </div>
                  <div class="email-item">
                    <div class="email-type yahoo">Yahoo</div>
                    <div class="email-info">
                      <div class="email-name">个人邮箱</div>
                      <div class="email-price">¥9.99</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="container">
        <div class="section-header">
          <h2>为什么选择我们</h2>
          <p>专业、可靠、高效的邮箱服务</p>
        </div>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
              </svg>
            </div>
            <h3>安全可靠</h3>
            <p>所有邮箱均经过严格验证，确保账户安全和数据保护</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
            </div>
            <h3>即时交付</h3>
            <p>支付完成后立即交付邮箱账户信息，无需等待</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
              </svg>
            </div>
            <h3>24/7 支持</h3>
            <p>专业客服团队全天候在线，随时解答您的问题</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
              </svg>
            </div>
            <h3>高性价比</h3>
            <p>提供市场上最具竞争力的价格，物超所值</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Products Preview -->
    <section class="products-preview-section">
      <div class="container">
        <div class="section-header">
          <h2>热门产品</h2>
          <p>精选最受欢迎的邮箱服务</p>
        </div>
        <div class="products-preview-grid">
          <div v-for="product in featuredProducts" :key="product.id" class="product-preview-card">
            <div class="product-header">
              <div class="product-type" :class="product.type">
                {{ product.type.toUpperCase() }}
              </div>
              <div class="product-badge" v-if="product.popularity > 90">
                热门
              </div>
            </div>
            <div class="product-info">
              <h3>{{ product.name }}</h3>
              <p>{{ product.description }}</p>
              <div class="product-features">
                <span v-for="feature in product.features.slice(0, 2)" :key="feature" class="feature-tag">
                  {{ feature }}
                </span>
              </div>
              <div class="product-price">¥{{ product.price }}</div>
            </div>
            <router-link to="/products" class="btn btn-primary btn-full">
              查看详情
            </router-link>
          </div>
        </div>
        <div class="section-footer">
          <router-link to="/products" class="btn btn-outline">
            查看全部产品
          </router-link>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2>准备开始了吗？</h2>
          <p>立即选择适合您的邮箱服务，享受专业的邮件体验</p>
          <router-link to="/products" class="btn btn-accent btn-large">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M5 12h14M12 5l7 7-7 7"/>
            </svg>
            <span>立即开始</span>
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.home-page {
  display: flex;
  flex-direction: column;
}

/* Hero Section */
.hero-section {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-primary-bg);
  position: relative;
  overflow: hidden;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
  min-height: 500px;
}

.hero-title {
  font-size: 3rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #333;
  margin-bottom: var(--spacing-2xl);
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-lg);
}

.btn-large {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: 1.125rem;
}

/* Hero Graphic */
.hero-window {
  max-width: 400px;
  margin: 0 auto;
}

.email-preview {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.email-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--color-neutral-white);
  border: 1px solid #e5e7eb;
  border-radius: var(--border-radius);
}

.email-type {
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  text-transform: uppercase;
}

.email-type.gmail {
  background-color: #fef3c7;
  color: #92400e;
}

.email-type.outlook {
  background-color: #dbeafe;
  color: #1e40af;
}

.email-type.yahoo {
  background-color: #e0e7ff;
  color: #5b21b6;
}

.email-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.email-name {
  font-weight: var(--font-weight-medium);
}

.email-price {
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-black);
}

/* Features Section */
.features-section {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-neutral-white);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.section-header h2 {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.section-header p {
  font-size: 1.125rem;
  color: #666;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.feature-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--color-secondary-surface);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
}

.feature-icon {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
  color: var(--color-neutral-black);
}

.feature-card h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-md);
}

.feature-card p {
  color: #333;
  line-height: 1.6;
}

/* Products Preview */
.products-preview-section {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-primary-bg);
}

.products-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.product-preview-card {
  background-color: var(--color-secondary-surface);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
  overflow: hidden;
  transition: all 0.2s ease;
}

.product-preview-card:hover {
  transform: translate(1px, 1px);
  box-shadow: 2px 2px 0 var(--color-neutral-black);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-neutral-white);
  border-bottom: var(--border-width) solid var(--color-neutral-black);
}

.product-badge {
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--color-accent-highlight);
  color: var(--color-neutral-white);
  border-radius: var(--border-radius);
}

.product-info {
  padding: var(--spacing-lg);
}

.product-info h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-sm);
}

.product-info p {
  color: #333;
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
}

.product-features {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.feature-tag {
  font-size: 0.75rem;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--color-neutral-white);
  border: 1px solid var(--color-neutral-black);
  border-radius: var(--border-radius);
}

.product-info .product-price {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
}

.btn-full {
  width: 100%;
  justify-content: center;
  margin: 0 var(--spacing-lg) var(--spacing-lg);
}

.section-footer {
  text-align: center;
}

/* CTA Section */
.cta-section {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-neutral-black);
  color: var(--color-neutral-white);
}

.cta-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.cta-content h2 {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.cta-content p {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-2xl);
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .products-preview-grid {
    grid-template-columns: 1fr;
  }
}
</style>
