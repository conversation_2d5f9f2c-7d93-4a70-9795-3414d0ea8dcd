<template>
  <div class="dashboard-page">
    <div class="container">
      <div class="dashboard-header">
        <h1>仪表板</h1>
        <p>欢迎回来，{{ userName }}！</p>
      </div>

      <div class="dashboard-grid">
        <!-- Stats Cards -->
        <div class="stats-section">
          <div class="card stats-card">
            <div class="card-header">
              <h3>账户统计</h3>
            </div>
            <div class="card-body">
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ userStats.totalOrders }}</div>
                  <div class="stat-label">总订单</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ userStats.activeEmails }}</div>
                  <div class="stat-label">活跃邮箱</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">¥{{ userStats.totalSpent }}</div>
                  <div class="stat-label">总消费</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ userStats.memberDays }}</div>
                  <div class="stat-label">会员天数</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="actions-section">
          <div class="card actions-card">
            <div class="card-header">
              <h3>快速操作</h3>
            </div>
            <div class="card-body">
              <div class="actions-grid">
                <router-link to="/products" class="action-btn">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="9" cy="21" r="1"/>
                    <circle cx="20" cy="21" r="1"/>
                    <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
                  </svg>
                  <span>浏览产品</span>
                </router-link>
                <router-link to="/cart" class="action-btn">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2v-4.01"/>
                  </svg>
                  <span>查看购物车</span>
                </router-link>
                <router-link to="/profile" class="action-btn">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                    <circle cx="12" cy="7" r="4"/>
                  </svg>
                  <span>个人资料</span>
                </router-link>
                <a href="#" class="action-btn" @click.prevent="handleSupport">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                  </svg>
                  <span>客户支持</span>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Orders -->
        <div class="orders-section">
          <div class="card orders-card">
            <div class="card-header">
              <h3>最近订单</h3>
              <router-link to="/orders" class="view-all-link">查看全部</router-link>
            </div>
            <div class="card-body">
              <div v-if="recentOrders.length === 0" class="empty-state">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                  <line x1="9" y1="9" x2="9.01" y2="9"/>
                  <line x1="15" y1="9" x2="15.01" y2="9"/>
                </svg>
                <p>暂无订单记录</p>
                <router-link to="/products" class="btn btn-primary">开始购买</router-link>
              </div>
              <div v-else class="orders-list">
                <div v-for="order in recentOrders" :key="order.id" class="order-item">
                  <div class="order-info">
                    <div class="order-id">#{{ order.id }}</div>
                    <div class="order-date">{{ formatDate(order.createdAt) }}</div>
                  </div>
                  <div class="order-details">
                    <div class="order-items">{{ order.itemCount }} 个商品</div>
                    <div class="order-total">¥{{ order.total }}</div>
                  </div>
                  <div class="order-status" :class="order.status">
                    {{ getStatusText(order.status) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recommended Products -->
        <div class="recommendations-section">
          <div class="card recommendations-card">
            <div class="card-header">
              <h3>推荐产品</h3>
            </div>
            <div class="card-body">
              <div class="products-grid">
                <div v-for="product in recommendedProducts" :key="product.id" class="product-card">
                  <div class="product-info">
                    <h4 class="product-name">{{ product.name }}</h4>
                    <p class="product-description">{{ product.description }}</p>
                    <div class="product-price">¥{{ product.price }}</div>
                  </div>
                  <button class="btn btn-primary btn-sm" @click="addToCart(product)">
                    加入购物车
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import { useCartStore } from '../stores/cart'
import { useProductsStore } from '../stores/products'

const authStore = useAuthStore()
const cartStore = useCartStore()
const productsStore = useProductsStore()

// Reactive state
const userStats = ref({
  totalOrders: 0,
  activeEmails: 0,
  totalSpent: 0,
  memberDays: 0
})

const recentOrders = ref([])
const recommendedProducts = ref([])

// Computed properties
const userName = computed(() => authStore.userName)

// Methods
const loadDashboardData = async () => {
  // TODO: Replace with actual API calls
  // Mock data for development
  userStats.value = {
    totalOrders: 5,
    activeEmails: 3,
    totalSpent: 89.97,
    memberDays: Math.floor((Date.now() - new Date('2024-01-01').getTime()) / (1000 * 60 * 60 * 24))
  }

  recentOrders.value = [
    {
      id: 'ORD001',
      itemCount: 2,
      total: 28.98,
      status: 'completed',
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 'ORD002',
      itemCount: 1,
      total: 15.99,
      status: 'processing',
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
    }
  ]

  // Get recommended products from store
  await productsStore.initializeProducts()
  recommendedProducts.value = productsStore.products.slice(0, 3)
}

const addToCart = (product) => {
  cartStore.addItem(product)
  // TODO: Show success notification
}

const handleSupport = () => {
  // TODO: Open support chat or redirect to support page
  alert('客户支持功能即将推出！')
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// Lifecycle
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard-page {
  padding: var(--spacing-2xl) 0;
}

.dashboard-header {
  margin-bottom: var(--spacing-2xl);
}

.dashboard-header h1 {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

.dashboard-header p {
  color: #666;
  font-size: 1.125rem;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

/* Stats Section */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-black);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.875rem;
  color: #666;
  text-transform: uppercase;
}

/* Actions Section */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  text-decoration: none;
  color: var(--color-neutral-black);
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: var(--color-secondary-surface);
  transform: translate(1px, 1px);
  box-shadow: 1px 1px 0 var(--color-neutral-black);
}

.action-btn span {
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  text-align: center;
}

/* Orders Section */
.view-all-link {
  font-size: 0.875rem;
  color: var(--color-neutral-black);
  text-decoration: underline;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: #666;
}

.empty-state svg {
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state p {
  margin-bottom: var(--spacing-lg);
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--color-neutral-white);
  border: 1px solid #e5e7eb;
  border-radius: var(--border-radius);
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.order-id {
  font-weight: var(--font-weight-bold);
  font-size: 0.875rem;
}

.order-date {
  font-size: 0.75rem;
  color: #666;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  text-align: right;
}

.order-items {
  font-size: 0.875rem;
  color: #666;
}

.order-total {
  font-weight: var(--font-weight-bold);
}

.order-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
}

.order-status.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.order-status.processing {
  background-color: #dbeafe;
  color: #1e40af;
}

.order-status.completed {
  background-color: #d1fae5;
  color: #065f46;
}

.order-status.cancelled {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Recommendations Section */
.products-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.product-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--color-neutral-white);
  border: 1px solid #e5e7eb;
  border-radius: var(--border-radius);
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 0.875rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.product-description {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: var(--spacing-xs);
}

.product-price {
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-black);
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.75rem;
}

@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .order-item,
  .product-card {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .order-details {
    align-self: stretch;
    text-align: left;
  }
}
</style>
