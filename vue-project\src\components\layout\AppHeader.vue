<template>
  <header class="app-header">
    <div class="container">
      <div class="header-content">
        <!-- Logo Section -->
        <div class="logo-section">
          <router-link to="/" class="logo">
            <div class="logo-icon">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                <rect x="2" y="6" width="28" height="20" rx="2" stroke="currentColor" stroke-width="2"/>
                <path d="M2 8L16 18L30 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <span class="logo-text">邮箱售卖</span>
          </router-link>
        </div>

        <!-- Navigation -->
        <nav class="main-nav" v-if="!isMobile || showMobileMenu">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link to="/products" class="nav-link">产品</router-link>
          <router-link to="/about" class="nav-link">关于</router-link>
        </nav>

        <!-- Search Bar -->
        <div class="search-section" v-if="!isMobile">
          <div class="search-input-wrapper">
            <input 
              type="text" 
              class="search-input" 
              placeholder="搜索邮箱类型..."
              v-model="searchQuery"
              @keyup.enter="handleSearch"
            >
            <button class="search-btn" @click="handleSearch">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <circle cx="7" cy="7" r="5" stroke="currentColor" stroke-width="2"/>
                <path d="m13 13 -3 -3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- User Actions -->
        <div class="user-actions">
          <!-- Cart -->
          <router-link to="/cart" class="action-btn cart-btn" v-if="isAuthenticated">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V16.5M9 19.5C9.8 19.5 10.5 18.8 10.5 18S9.8 16.5 9 16.5 7.5 17.2 7.5 18 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 18.8 21.5 18S20.8 16.5 20 16.5 18.5 17.2 18.5 18 19.2 19.5 20 19.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span class="cart-count" v-if="cartItemCount > 0">{{ cartItemCount }}</span>
          </router-link>

          <!-- User Menu -->
          <div class="user-menu" v-if="isAuthenticated">
            <button class="action-btn user-btn" @click="toggleUserMenu">
              <div class="user-avatar">
                <span>{{ userInitials }}</span>
              </div>
            </button>
            
            <!-- User Dropdown -->
            <div class="user-dropdown" v-if="showUserMenu">
              <div class="user-info">
                <div class="user-name">{{ userName }}</div>
                <div class="user-email">{{ user?.email }}</div>
              </div>
              <div class="dropdown-divider"></div>
              <router-link to="/dashboard" class="dropdown-item" @click="closeUserMenu">
                仪表板
              </router-link>
              <router-link to="/profile" class="dropdown-item" @click="closeUserMenu">
                个人资料
              </router-link>
              <div class="dropdown-divider"></div>
              <button class="dropdown-item logout-btn" @click="handleLogout">
                登出
              </button>
            </div>
          </div>

          <!-- Auth Buttons -->
          <div class="auth-buttons" v-else>
            <router-link to="/login" class="btn btn-outline">登录</router-link>
            <router-link to="/register" class="btn btn-primary">注册</router-link>
          </div>

          <!-- Mobile Menu Toggle -->
          <button class="mobile-menu-btn" @click="toggleMobileMenu" v-if="isMobile">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay" v-if="isMobile && showMobileMenu" @click="closeMobileMenu">
      <div class="mobile-menu" @click.stop>
        <div class="mobile-search">
          <input 
            type="text" 
            class="search-input" 
            placeholder="搜索邮箱类型..."
            v-model="searchQuery"
            @keyup.enter="handleSearch"
          >
        </div>
        <nav class="mobile-nav">
          <router-link to="/" class="nav-link" @click="closeMobileMenu">首页</router-link>
          <router-link to="/products" class="nav-link" @click="closeMobileMenu">产品</router-link>
          <router-link to="/about" class="nav-link" @click="closeMobileMenu">关于</router-link>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useCartStore } from '../../stores/cart'
import { useProductsStore } from '../../stores/products'

const router = useRouter()
const authStore = useAuthStore()
const cartStore = useCartStore()
const productsStore = useProductsStore()

// Reactive state
const searchQuery = ref('')
const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const isMobile = ref(false)

// Computed properties
const isAuthenticated = computed(() => authStore.isAuthenticated)
const user = computed(() => authStore.user)
const userName = computed(() => authStore.userName)
const cartItemCount = computed(() => cartStore.itemCount)

const userInitials = computed(() => {
  if (!user.value) return 'U'
  const name = user.value.name || user.value.email
  return name.charAt(0).toUpperCase()
})

// Methods
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    productsStore.updateFilters({ searchQuery: searchQuery.value.trim() })
    router.push('/products')
    closeMobileMenu()
  }
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const closeUserMenu = () => {
  showUserMenu.value = false
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

const handleLogout = async () => {
  await authStore.logout()
  closeUserMenu()
  router.push('/')
}

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

const handleClickOutside = (event) => {
  if (showUserMenu.value && !event.target.closest('.user-menu')) {
    closeUserMenu()
  }
}

// Lifecycle
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.app-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: var(--color-neutral-white);
  border-bottom: var(--border-width) solid var(--color-neutral-black);
  box-shadow: 0 var(--shadow-offset) 0 var(--color-neutral-black);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--header-height);
  gap: var(--spacing-lg);
}

/* Logo Section */
.logo-section {
  flex-shrink: 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  font-size: 1.25rem;
  color: var(--color-neutral-black);
  text-decoration: none;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--color-secondary-surface);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
}

/* Navigation */
.main-nav {
  display: flex;
  gap: var(--spacing-lg);
}

.nav-link {
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-black);
  text-decoration: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.nav-link:hover,
.nav-link.router-link-active {
  background-color: var(--color-secondary-surface);
  transform: translate(1px, 1px);
}

/* Search Section */
.search-section {
  flex: 1;
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
}

.search-input {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  padding-right: 40px;
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  background-color: var(--color-neutral-white);
}

.search-btn {
  position: absolute;
  right: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
}

/* User Actions */
.user-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-shrink: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  background-color: var(--color-neutral-white);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translate(1px, 1px);
  box-shadow: 1px 1px 0 var(--color-neutral-black);
}

.cart-btn {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--color-accent-highlight);
  color: var(--color-neutral-white);
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid var(--color-neutral-black);
  min-width: 18px;
  text-align: center;
}

/* User Menu */
.user-menu {
  position: relative;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--color-secondary-surface);
  border-radius: 50%;
  font-weight: var(--font-weight-bold);
  font-size: 0.875rem;
}

.user-dropdown {
  position: absolute;
  top: calc(100% + var(--spacing-sm));
  right: 0;
  min-width: 200px;
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
  padding: var(--spacing-md);
}

.user-info {
  margin-bottom: var(--spacing-sm);
}

.user-name {
  font-weight: var(--font-weight-bold);
  font-size: 0.875rem;
}

.user-email {
  font-size: 0.75rem;
  color: #666;
}

.dropdown-divider {
  height: 1px;
  background-color: var(--color-neutral-black);
  margin: var(--spacing-sm) 0;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--spacing-sm);
  text-align: left;
  color: var(--color-neutral-black);
  text-decoration: none;
  border: none;
  background: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: var(--color-secondary-surface);
}

.logout-btn {
  color: #dc2626;
}

/* Auth Buttons */
.auth-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-neutral-black);
}

.btn-outline:hover {
  background-color: var(--color-neutral-black);
  color: var(--color-neutral-white);
}

/* Mobile */
.mobile-menu-btn {
  display: none;
}

.mobile-menu-overlay {
  position: fixed;
  top: var(--header-height);
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
}

.mobile-menu {
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-top: none;
  padding: var(--spacing-lg);
}

.mobile-search {
  margin-bottom: var(--spacing-lg);
}

.mobile-nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

@media (max-width: 768px) {
  .main-nav,
  .search-section {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .auth-buttons {
    gap: var(--spacing-xs);
  }
  
  .auth-buttons .btn {
    padding: var(--spacing-sm);
    font-size: 0.75rem;
  }
}
</style>
