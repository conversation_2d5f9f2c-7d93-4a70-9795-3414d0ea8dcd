{"name": "is-stream", "version": "4.0.1", "description": "Check if something is a Node.js stream", "license": "MIT", "repository": "sindresorhus/is-stream", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "devDependencies": {"@types/node": "^20.11.19", "ava": "^5.3.1", "tempy": "^3.1.0", "tsd": "^0.30.5", "xo": "^0.57.0"}}