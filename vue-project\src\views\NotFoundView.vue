<template>
  <div class="not-found-page">
    <div class="container">
      <div class="not-found-content">
        <!-- Error Window -->
        <div class="error-window">
          <div class="window-header">
            <div class="window-title">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="15" y1="9" x2="9" y2="15"/>
                <line x1="9" y1="9" x2="15" y2="15"/>
              </svg>
              <span>错误 404</span>
            </div>
            <div class="window-controls">
              <div class="window-control minimize"></div>
              <div class="window-control maximize"></div>
              <div class="window-control close"></div>
            </div>
          </div>
          
          <div class="error-content">
            <!-- 404 Illustration -->
            <div class="error-illustration">
              <div class="error-code">404</div>
              <div class="error-icon">
                <svg width="120" height="120" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                  <line x1="12" y1="9" x2="12" y2="13"/>
                  <line x1="12" y1="17" x2="12.01" y2="17"/>
                </svg>
              </div>
            </div>

            <!-- Error Message -->
            <div class="error-message">
              <h1>页面未找到</h1>
              <p>抱歉，您访问的页面不存在或已被移动。</p>
              <p>请检查URL是否正确，或者返回首页继续浏览。</p>
            </div>

            <!-- Action Buttons -->
            <div class="error-actions">
              <router-link to="/" class="btn btn-primary">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                  <polyline points="9,22 9,12 15,12 15,22"/>
                </svg>
                <span>返回首页</span>
              </router-link>
              
              <button class="btn btn-outline" @click="goBack">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="15,18 9,12 15,6"/>
                </svg>
                <span>返回上页</span>
              </button>
              
              <router-link to="/products" class="btn btn-outline">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="9" cy="21" r="1"/>
                  <circle cx="20" cy="21" r="1"/>
                  <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
                </svg>
                <span>浏览产品</span>
              </router-link>
            </div>

            <!-- Search Section -->
            <div class="search-section">
              <h3>或者搜索您需要的内容：</h3>
              <div class="search-form">
                <input 
                  type="text" 
                  class="input search-input"
                  placeholder="搜索邮箱类型、产品..."
                  v-model="searchQuery"
                  @keyup.enter="handleSearch"
                >
                <button class="btn btn-primary search-btn" @click="handleSearch">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"/>
                    <path d="m21 21-4.35-4.35"/>
                  </svg>
                  <span>搜索</span>
                </button>
              </div>
            </div>

            <!-- Quick Links -->
            <div class="quick-links">
              <h3>快速链接：</h3>
              <div class="links-grid">
                <router-link to="/products" class="quick-link">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="2" y="6" width="20" height="12" rx="2"/>
                    <path d="M2 8l10 6 10-6"/>
                  </svg>
                  <span>邮箱产品</span>
                </router-link>
                
                <router-link to="/about" class="quick-link">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                    <line x1="12" y1="17" x2="12.01" y2="17"/>
                  </svg>
                  <span>关于我们</span>
                </router-link>
                
                <a href="#" class="quick-link" @click.prevent="handleSupport">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                  </svg>
                  <span>客户支持</span>
                </a>
                
                <a href="#" class="quick-link" @click.prevent="handleContact">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                    <polyline points="22,6 12,13 2,6"/>
                  </svg>
                  <span>联系我们</span>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Decorative Graphics -->
        <div class="decorative-graphics">
          <div class="graphic-circuit">
            <svg width="200" height="200" viewBox="0 0 200 200" fill="none">
              <rect x="20" y="20" width="160" height="160" rx="8" stroke="currentColor" stroke-width="2" stroke-dasharray="10,5"/>
              <circle cx="50" cy="50" r="8" stroke="currentColor" stroke-width="2"/>
              <circle cx="150" cy="50" r="8" stroke="currentColor" stroke-width="2"/>
              <circle cx="50" cy="150" r="8" stroke="currentColor" stroke-width="2"/>
              <circle cx="150" cy="150" r="8" stroke="currentColor" stroke-width="2"/>
              <line x1="50" y1="50" x2="150" y2="50" stroke="currentColor" stroke-width="2"/>
              <line x1="50" y1="150" x2="150" y2="150" stroke="currentColor" stroke-width="2"/>
              <line x1="50" y1="50" x2="50" y2="150" stroke="currentColor" stroke-width="2"/>
              <line x1="150" y1="50" x2="150" y2="150" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          
          <div class="graphic-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useProductsStore } from '../stores/products'

const router = useRouter()
const productsStore = useProductsStore()

// Reactive state
const searchQuery = ref('')

// Methods
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    productsStore.updateFilters({ searchQuery: searchQuery.value.trim() })
    router.push('/products')
  }
}

const handleSupport = () => {
  alert('客户支持功能即将推出！')
}

const handleContact = () => {
  alert('联系我们功能即将推出！')
}
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  background-color: var(--color-primary-bg);
  position: relative;
  overflow: hidden;
}

.not-found-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 800px;
}

.error-window {
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
  overflow: hidden;
}

.window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-accent-highlight);
  border-bottom: var(--border-width) solid var(--color-neutral-black);
  color: var(--color-neutral-white);
}

.window-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  font-size: 0.875rem;
  text-transform: uppercase;
}

.error-content {
  padding: var(--spacing-2xl);
  text-align: center;
}

/* Error Illustration */
.error-illustration {
  margin-bottom: var(--spacing-2xl);
  position: relative;
}

.error-code {
  font-size: 6rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-black);
  opacity: 0.1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.error-icon {
  position: relative;
  z-index: 2;
  color: var(--color-accent-highlight);
}

/* Error Message */
.error-message {
  margin-bottom: var(--spacing-2xl);
}

.error-message h1 {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.error-message p {
  color: #666;
  margin-bottom: var(--spacing-sm);
  line-height: 1.6;
}

/* Action Buttons */
.error-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-2xl);
}

.btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* Search Section */
.search-section {
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-xl);
  background-color: var(--color-secondary-surface);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
}

.search-section h3 {
  margin-bottom: var(--spacing-lg);
  font-size: 1.125rem;
}

.search-form {
  display: flex;
  gap: var(--spacing-md);
  max-width: 400px;
  margin: 0 auto;
}

.search-input {
  flex: 1;
}

.search-btn {
  flex-shrink: 0;
}

/* Quick Links */
.quick-links h3 {
  margin-bottom: var(--spacing-lg);
  font-size: 1.125rem;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.quick-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background-color: var(--color-neutral-white);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  text-decoration: none;
  color: var(--color-neutral-black);
  transition: all 0.2s ease;
}

.quick-link:hover {
  background-color: var(--color-secondary-surface);
  transform: translate(1px, 1px);
  box-shadow: 1px 1px 0 var(--color-neutral-black);
}

.quick-link span {
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
}

/* Decorative Graphics */
.decorative-graphics {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.graphic-circuit {
  position: absolute;
  top: 10%;
  right: 5%;
  color: var(--color-neutral-black);
  opacity: 0.1;
}

.graphic-dots {
  position: absolute;
  bottom: 10%;
  left: 5%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
}

.dot {
  width: 12px;
  height: 12px;
  background-color: var(--color-neutral-black);
  border-radius: 50%;
  opacity: 0.2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .not-found-page {
    padding: var(--spacing-md);
  }
  
  .error-content {
    padding: var(--spacing-xl);
  }
  
  .error-code {
    font-size: 4rem;
  }
  
  .error-message h1 {
    font-size: 1.5rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .links-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .decorative-graphics {
    display: none;
  }
}

@media (max-width: 480px) {
  .links-grid {
    grid-template-columns: 1fr;
  }
}
</style>
