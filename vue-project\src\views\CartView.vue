<template>
  <div class="cart-page">
    <div class="container">
      <!-- Page Header -->
      <div class="page-header">
        <h1>购物车</h1>
        <p v-if="!isEmpty">您有 {{ itemCount }} 个商品在购物车中</p>
        <p v-else>您的购物车是空的</p>
      </div>

      <!-- Empty Cart State -->
      <div v-if="isEmpty" class="empty-cart">
        <div class="empty-cart-content">
          <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="9" cy="21" r="1"/>
            <circle cx="20" cy="21" r="1"/>
            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
          </svg>
          <h2>购物车是空的</h2>
          <p>看起来您还没有添加任何商品到购物车</p>
          <router-link to="/products" class="btn btn-primary">
            开始购物
          </router-link>
        </div>
      </div>

      <!-- Cart Content -->
      <div v-else class="cart-content">
        <!-- Cart Items -->
        <div class="cart-items-section">
          <div class="window">
            <div class="window-header">
              <div class="window-title">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2v-4.01"/>
                </svg>
                <span>购物车商品</span>
              </div>
              <button class="btn btn-outline btn-sm" @click="clearCart">
                清空购物车
              </button>
            </div>
            <div class="cart-items">
              <div v-for="item in cartItems" :key="item.id" class="cart-item">
                <!-- Item Info -->
                <div class="item-info">
                  <div class="item-type" :class="item.type">
                    {{ item.type.toUpperCase() }}
                  </div>
                  <h3 class="item-name">{{ item.name }}</h3>
                  <p class="item-description">{{ item.description }}</p>
                  <div class="item-price">¥{{ item.price }}</div>
                </div>

                <!-- Quantity Controls -->
                <div class="quantity-controls">
                  <label class="quantity-label">数量</label>
                  <div class="quantity-input-group">
                    <button 
                      class="quantity-btn"
                      @click="decreaseQuantity(item.id)"
                      :disabled="item.quantity <= 1"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="5" y1="12" x2="19" y2="12"/>
                      </svg>
                    </button>
                    <input 
                      type="number" 
                      class="quantity-input"
                      :value="item.quantity"
                      @input="updateQuantity(item.id, $event.target.value)"
                      min="1"
                      max="10"
                    >
                    <button 
                      class="quantity-btn"
                      @click="increaseQuantity(item.id)"
                      :disabled="item.quantity >= 10"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"/>
                        <line x1="5" y1="12" x2="19" y2="12"/>
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Item Total -->
                <div class="item-total">
                  <div class="total-label">小计</div>
                  <div class="total-amount">¥{{ (item.price * item.quantity).toFixed(2) }}</div>
                </div>

                <!-- Remove Button -->
                <button class="remove-btn" @click="removeItem(item.id)">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="3,6 5,6 21,6"/>
                    <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"/>
                    <line x1="10" y1="11" x2="10" y2="17"/>
                    <line x1="14" y1="11" x2="14" y2="17"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Cart Summary -->
        <div class="cart-summary-section">
          <div class="window">
            <div class="window-header">
              <div class="window-title">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 11H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h4m6-6h4a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-4m-6 0a2 2 0 0 0-2-2v-3a2 2 0 0 0 2-2m6 0a2 2 0 0 1 2-2v-3a2 2 0 0 1-2-2"/>
                </svg>
                <span>订单摘要</span>
              </div>
            </div>
            <div class="summary-content">
              <div class="summary-row">
                <span>商品总计</span>
                <span>¥{{ totalPrice.toFixed(2) }}</span>
              </div>
              <div class="summary-row">
                <span>运费</span>
                <span>免费</span>
              </div>
              <div class="summary-divider"></div>
              <div class="summary-row total-row">
                <span>总计</span>
                <span>¥{{ totalPrice.toFixed(2) }}</span>
              </div>

              <!-- Checkout Button -->
              <button 
                class="btn btn-primary btn-full checkout-btn"
                @click="proceedToCheckout"
                :disabled="isLoading"
              >
                <svg v-if="isLoading" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="loading-spinner">
                  <path d="M21 12a9 9 0 1 1-6.219-8.56"/>
                </svg>
                <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="1" y="3" width="15" height="13"/>
                  <polygon points="16,8 20,8 23,11 23,16 16,16 16,8"/>
                  <circle cx="5.5" cy="18.5" r="2.5"/>
                  <circle cx="18.5" cy="18.5" r="2.5"/>
                </svg>
                <span>{{ isLoading ? '处理中...' : '立即结算' }}</span>
              </button>

              <!-- Continue Shopping -->
              <router-link to="/products" class="btn btn-outline btn-full">
                继续购物
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart'

const router = useRouter()
const cartStore = useCartStore()

// Computed properties
const cartItems = computed(() => cartStore.items)
const itemCount = computed(() => cartStore.itemCount)
const totalPrice = computed(() => cartStore.totalPrice)
const isEmpty = computed(() => cartStore.isEmpty)
const isLoading = computed(() => cartStore.isLoading)

// Methods
const updateQuantity = (productId, quantity) => {
  const qty = parseInt(quantity)
  if (qty > 0 && qty <= 10) {
    cartStore.updateQuantity(productId, qty)
  }
}

const increaseQuantity = (productId) => {
  const item = cartItems.value.find(item => item.id === productId)
  if (item && item.quantity < 10) {
    cartStore.updateQuantity(productId, item.quantity + 1)
  }
}

const decreaseQuantity = (productId) => {
  const item = cartItems.value.find(item => item.id === productId)
  if (item && item.quantity > 1) {
    cartStore.updateQuantity(productId, item.quantity - 1)
  }
}

const removeItem = (productId) => {
  cartStore.removeItem(productId)
}

const clearCart = () => {
  if (confirm('确定要清空购物车吗？')) {
    cartStore.clearCart()
  }
}

const proceedToCheckout = async () => {
  // TODO: Implement actual checkout process
  const result = await cartStore.checkout({
    method: 'mock_payment'
  })
  
  if (result.success) {
    alert(`订单创建成功！订单号：${result.order.id}`)
    router.push('/dashboard')
  } else {
    alert(`结算失败：${result.error}`)
  }
}
</script>

<style scoped>
.cart-page {
  padding: var(--spacing-2xl) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.page-header h1 {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

.page-header p {
  color: #666;
  font-size: 1.125rem;
}

/* Empty Cart */
.empty-cart {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-cart-content {
  text-align: center;
  max-width: 400px;
}

.empty-cart-content svg {
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-cart-content h2 {
  margin-bottom: var(--spacing-md);
}

.empty-cart-content p {
  color: #666;
  margin-bottom: var(--spacing-xl);
}

/* Cart Content */
.cart-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
}

/* Cart Items */
.cart-items {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.cart-item {
  display: grid;
  grid-template-columns: 1fr auto auto auto;
  gap: var(--spacing-lg);
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--color-neutral-white);
  border: 1px solid #e5e7eb;
  border-radius: var(--border-radius);
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.item-type {
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  text-transform: uppercase;
  width: fit-content;
}

.item-type.gmail {
  background-color: #fef3c7;
  color: #92400e;
}

.item-type.outlook {
  background-color: #dbeafe;
  color: #1e40af;
}

.item-type.yahoo {
  background-color: #e0e7ff;
  color: #5b21b6;
}

.item-name {
  font-size: 1.125rem;
  font-weight: var(--font-weight-bold);
}

.item-description {
  color: #666;
  font-size: 0.875rem;
}

.item-price {
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-black);
}

/* Quantity Controls */
.quantity-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  align-items: center;
}

.quantity-label {
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.quantity-input-group {
  display: flex;
  align-items: center;
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.quantity-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--color-neutral-white);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.quantity-btn:hover:not(:disabled) {
  background-color: var(--color-secondary-surface);
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-btn:not(:last-child) {
  border-right: 1px solid var(--color-neutral-black);
}

.quantity-btn:not(:first-child) {
  border-left: 1px solid var(--color-neutral-black);
}

.quantity-input {
  width: 60px;
  height: 32px;
  text-align: center;
  border: none;
  outline: none;
  font-weight: var(--font-weight-medium);
}

/* Item Total */
.item-total {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  align-items: center;
}

.total-label {
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  color: #666;
}

.total-amount {
  font-size: 1.125rem;
  font-weight: var(--font-weight-bold);
}

/* Remove Button */
.remove-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: transparent;
  border: var(--border-width) solid #dc2626;
  border-radius: var(--border-radius);
  color: #dc2626;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background-color: #dc2626;
  color: var(--color-neutral-white);
  transform: translate(1px, 1px);
}

/* Cart Summary */
.summary-content {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.summary-divider {
  height: 1px;
  background-color: var(--color-neutral-black);
  margin: var(--spacing-sm) 0;
}

.total-row {
  font-size: 1.125rem;
  font-weight: var(--font-weight-bold);
}

.checkout-btn {
  margin-top: var(--spacing-lg);
  gap: var(--spacing-sm);
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .cart-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .cart-item {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    text-align: center;
  }
  
  .quantity-controls,
  .item-total {
    align-items: center;
  }
}
</style>
