import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const token = ref(localStorage.getItem('auth_token') || null)
  const isLoading = ref(false)
  const error = ref(null)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role || 'user')
  const userName = computed(() => user.value?.name || user.value?.email || '')

  // Actions
  const login = async (credentials) => {
    isLoading.value = true
    error.value = null
    
    try {
      // TODO: Replace with actual API call
      // const response = await authAPI.login(credentials)
      
      // Mock login for development
      const mockResponse = {
        user: {
          id: 1,
          email: credentials.email,
          name: credentials.email.split('@')[0],
          role: 'user',
          avatar: null,
          createdAt: new Date().toISOString()
        },
        token: 'mock_jwt_token_' + Date.now()
      }
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Set user and token
      user.value = mockResponse.user
      token.value = mockResponse.token
      
      // Store token in localStorage
      localStorage.setItem('auth_token', mockResponse.token)
      
      return { success: true, user: mockResponse.user }
    } catch (err) {
      error.value = err.message || '登录失败'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    isLoading.value = true
    error.value = null
    
    try {
      // TODO: Replace with actual API call
      // const response = await authAPI.register(userData)
      
      // Mock registration for development
      const mockResponse = {
        user: {
          id: Date.now(),
          email: userData.email,
          name: userData.name || userData.email.split('@')[0],
          role: 'user',
          avatar: null,
          createdAt: new Date().toISOString()
        },
        token: 'mock_jwt_token_' + Date.now()
      }
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Set user and token
      user.value = mockResponse.user
      token.value = mockResponse.token
      
      // Store token in localStorage
      localStorage.setItem('auth_token', mockResponse.token)
      
      return { success: true, user: mockResponse.user }
    } catch (err) {
      error.value = err.message || '注册失败'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    isLoading.value = true
    
    try {
      // TODO: Replace with actual API call to invalidate token
      // await authAPI.logout()
      
      // Clear local state
      user.value = null
      token.value = null
      error.value = null
      
      // Remove token from localStorage
      localStorage.removeItem('auth_token')
      
      return { success: true }
    } catch (err) {
      error.value = err.message || '登出失败'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const fetchUser = async () => {
    if (!token.value) return { success: false, error: '未找到认证令牌' }
    
    isLoading.value = true
    error.value = null
    
    try {
      // TODO: Replace with actual API call
      // const response = await authAPI.getUser()
      
      // Mock user fetch for development
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user',
        avatar: null,
        createdAt: new Date().toISOString()
      }
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      user.value = mockUser
      return { success: true, user: mockUser }
    } catch (err) {
      error.value = err.message || '获取用户信息失败'
      // If token is invalid, clear it
      if (err.status === 401) {
        logout()
      }
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const updateProfile = async (profileData) => {
    isLoading.value = true
    error.value = null
    
    try {
      // TODO: Replace with actual API call
      // const response = await authAPI.updateProfile(profileData)
      
      // Mock profile update for development
      const updatedUser = {
        ...user.value,
        ...profileData,
        updatedAt: new Date().toISOString()
      }
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      user.value = updatedUser
      return { success: true, user: updatedUser }
    } catch (err) {
      error.value = err.message || '更新资料失败'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Initialize auth state on store creation
  const initializeAuth = async () => {
    if (token.value) {
      await fetchUser()
    }
  }

  return {
    // State
    user,
    token,
    isLoading,
    error,
    
    // Getters
    isAuthenticated,
    userRole,
    userName,
    
    // Actions
    login,
    register,
    logout,
    fetchUser,
    updateProfile,
    clearError,
    initializeAuth
  }
})
