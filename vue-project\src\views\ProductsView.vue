<template>
  <div class="products-page">
    <div class="container">
      <!-- Page Header -->
      <div class="page-header">
        <h1>产品列表</h1>
        <p>选择适合您需求的邮箱服务</p>
      </div>

      <!-- Filters and Search -->
      <div class="filters-section">
        <div class="window">
          <div class="window-header">
            <div class="window-title">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"/>
              </svg>
              <span>筛选和搜索</span>
            </div>
          </div>
          <div class="filters-content">
            <!-- Search -->
            <div class="filter-group">
              <label class="filter-label">搜索</label>
              <input
                type="text"
                class="input"
                placeholder="搜索邮箱类型..."
                v-model="searchQuery"
                @input="updateSearch"
              >
            </div>

            <!-- Category Filter -->
            <div class="filter-group">
              <label class="filter-label">分类</label>
              <select class="input" v-model="selectedCategory" @change="updateCategory">
                <option value="">全部分类</option>
                <option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </option>
              </select>
            </div>

            <!-- Price Range -->
            <div class="filter-group">
              <label class="filter-label">价格范围</label>
              <div class="price-range">
                <input
                  type="number"
                  class="input price-input"
                  placeholder="最低价"
                  v-model.number="priceRange[0]"
                  @input="updatePriceRange"
                >
                <span class="price-separator">-</span>
                <input
                  type="number"
                  class="input price-input"
                  placeholder="最高价"
                  v-model.number="priceRange[1]"
                  @input="updatePriceRange"
                >
              </div>
            </div>

            <!-- Sort -->
            <div class="filter-group">
              <label class="filter-label">排序</label>
              <select class="input" v-model="sortBy" @change="updateSort">
                <option value="name">按名称</option>
                <option value="price">按价格</option>
                <option value="popularity">按热度</option>
              </select>
            </div>

            <!-- Reset Filters -->
            <button class="btn btn-outline" @click="resetFilters">
              重置筛选
            </button>
          </div>
        </div>
      </div>

      <!-- Products Grid -->
      <div class="products-section">
        <div v-if="isLoading" class="loading-state">
          <div class="loading-spinner">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 12a9 9 0 1 1-6.219-8.56"/>
            </svg>
          </div>
          <p>加载产品中...</p>
        </div>

        <div v-else-if="filteredProducts.length === 0" class="empty-state">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.35-4.35"/>
          </svg>
          <h3>未找到匹配的产品</h3>
          <p>请尝试调整筛选条件或搜索关键词</p>
          <button class="btn btn-primary" @click="resetFilters">
            重置筛选
          </button>
        </div>

        <div v-else class="products-grid">
          <div v-for="product in filteredProducts" :key="product.id" class="product-card">
            <!-- Product Header -->
            <div class="product-header">
              <div class="product-type" :class="product.type">
                {{ product.type.toUpperCase() }}
              </div>
              <div class="product-availability" :class="product.availability">
                {{ getAvailabilityText(product.availability) }}
              </div>
            </div>

            <!-- Product Info -->
            <div class="product-info">
              <h3 class="product-name">{{ product.name }}</h3>
              <p class="product-description">{{ product.description }}</p>
              
              <!-- Features -->
              <div class="product-features">
                <div v-for="feature in product.features" :key="feature" class="feature-tag">
                  {{ feature }}
                </div>
              </div>

              <!-- Price -->
              <div class="product-price">
                <span class="price-currency">¥</span>
                <span class="price-amount">{{ product.price }}</span>
              </div>
            </div>

            <!-- Product Actions -->
            <div class="product-actions">
              <button 
                class="btn btn-primary btn-full"
                :disabled="product.availability === 'out_of_stock'"
                @click="addToCart(product)"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="9" cy="21" r="1"/>
                  <circle cx="20" cy="21" r="1"/>
                  <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
                </svg>
                <span>{{ product.availability === 'out_of_stock' ? '暂时缺货' : '加入购物车' }}</span>
              </button>
              <button class="btn btn-outline" @click="viewDetails(product)">
                查看详情
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useProductsStore } from '../stores/products'
import { useCartStore } from '../stores/cart'

const productsStore = useProductsStore()
const cartStore = useCartStore()

// Reactive state
const searchQuery = ref('')
const selectedCategory = ref('')
const priceRange = ref([0, 1000])
const sortBy = ref('name')

// Computed properties
const isLoading = computed(() => productsStore.isLoading)
const filteredProducts = computed(() => productsStore.filteredProducts)
const categories = computed(() => productsStore.categories)

// Methods
const updateSearch = () => {
  productsStore.updateFilters({ searchQuery: searchQuery.value })
}

const updateCategory = () => {
  productsStore.updateFilters({ category: selectedCategory.value })
}

const updatePriceRange = () => {
  productsStore.updateFilters({ priceRange: priceRange.value })
}

const updateSort = () => {
  productsStore.updateFilters({ sortBy: sortBy.value })
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  priceRange.value = [0, 1000]
  sortBy.value = 'name'
  productsStore.resetFilters()
}

const addToCart = (product) => {
  cartStore.addItem(product)
  // TODO: Show success notification
  console.log(`Added ${product.name} to cart`)
}

const viewDetails = (product) => {
  // TODO: Navigate to product details page or show modal
  console.log(`View details for ${product.name}`)
}

const getAvailabilityText = (availability) => {
  const availabilityMap = {
    in_stock: '现货',
    limited: '限量',
    out_of_stock: '缺货'
  }
  return availabilityMap[availability] || availability
}

// Watch for filter changes in store
watch(() => productsStore.filters, (newFilters) => {
  searchQuery.value = newFilters.searchQuery
  selectedCategory.value = newFilters.category
  priceRange.value = [...newFilters.priceRange]
  sortBy.value = newFilters.sortBy
}, { deep: true })

// Lifecycle
onMounted(async () => {
  await productsStore.initializeProducts()
})
</script>

<style scoped>
.products-page {
  padding: var(--spacing-2xl) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.page-header h1 {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

.page-header p {
  color: #666;
  font-size: 1.125rem;
}

/* Filters Section */
.filters-section {
  margin-bottom: var(--spacing-2xl);
}

.filters-content {
  padding: var(--spacing-lg);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.filter-label {
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
  text-transform: uppercase;
}

.price-range {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.price-input {
  flex: 1;
}

.price-separator {
  font-weight: var(--font-weight-bold);
}

/* Loading and Empty States */
.loading-state,
.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: #666;
}

.loading-spinner svg {
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty-state svg {
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--color-neutral-black);
}

.empty-state p {
  margin-bottom: var(--spacing-lg);
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-xl);
}

.product-card {
  background-color: var(--color-secondary-surface);
  border: var(--border-width) solid var(--color-neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--color-neutral-black);
  overflow: hidden;
  transition: all 0.2s ease;
}

.product-card:hover {
  transform: translate(1px, 1px);
  box-shadow: 2px 2px 0 var(--color-neutral-black);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-neutral-white);
  border-bottom: var(--border-width) solid var(--color-neutral-black);
}

.product-type {
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  text-transform: uppercase;
}

.product-type.gmail {
  background-color: #fef3c7;
  color: #92400e;
}

.product-type.outlook {
  background-color: #dbeafe;
  color: #1e40af;
}

.product-type.yahoo {
  background-color: #e0e7ff;
  color: #5b21b6;
}

.product-availability {
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
}

.product-availability.in_stock {
  background-color: #d1fae5;
  color: #065f46;
}

.product-availability.limited {
  background-color: #fef3c7;
  color: #92400e;
}

.product-availability.out_of_stock {
  background-color: #fee2e2;
  color: #991b1b;
}

.product-info {
  padding: var(--spacing-lg);
}

.product-name {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
}

.product-description {
  color: #333;
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
}

.product-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.feature-tag {
  font-size: 0.75rem;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--color-neutral-white);
  border: 1px solid var(--color-neutral-black);
  border-radius: var(--border-radius);
}

.product-price {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
}

.price-currency {
  font-size: 1rem;
}

.price-amount {
  font-size: 1.5rem;
}

.product-actions {
  padding: var(--spacing-lg);
  padding-top: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.btn-full {
  width: 100%;
  justify-content: center;
  gap: var(--spacing-sm);
}

@media (max-width: 768px) {
  .filters-content {
    grid-template-columns: 1fr;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .price-range {
    flex-direction: column;
    align-items: stretch;
  }
  
  .price-separator {
    text-align: center;
  }
}
</style>
