import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useProductsStore = defineStore('products', () => {
  // State
  const products = ref([])
  const categories = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  const filters = ref({
    category: '',
    priceRange: [0, 1000],
    searchQuery: '',
    sortBy: 'name' // name, price, popularity
  })

  // Getters
  const filteredProducts = computed(() => {
    let filtered = [...products.value]
    
    // Filter by category
    if (filters.value.category) {
      filtered = filtered.filter(product => product.category === filters.value.category)
    }
    
    // Filter by price range
    filtered = filtered.filter(product => 
      product.price >= filters.value.priceRange[0] && 
      product.price <= filters.value.priceRange[1]
    )
    
    // Filter by search query
    if (filters.value.searchQuery) {
      const query = filters.value.searchQuery.toLowerCase()
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.type.toLowerCase().includes(query)
      )
    }
    
    // Sort products
    filtered.sort((a, b) => {
      switch (filters.value.sortBy) {
        case 'price':
          return a.price - b.price
        case 'popularity':
          return (b.popularity || 0) - (a.popularity || 0)
        case 'name':
        default:
          return a.name.localeCompare(b.name)
      }
    })
    
    return filtered
  })

  const productsByCategory = computed(() => {
    const grouped = {}
    products.value.forEach(product => {
      if (!grouped[product.category]) {
        grouped[product.category] = []
      }
      grouped[product.category].push(product)
    })
    return grouped
  })

  // Actions
  const fetchProducts = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      // TODO: Replace with actual API call
      // const response = await productsAPI.getProducts()
      
      // Mock products for development
      const mockProducts = [
        {
          id: 1,
          name: 'Gmail 教育邮箱',
          type: 'gmail',
          category: 'education',
          price: 15.99,
          description: '高质量Gmail教育邮箱，支持Google Drive无限存储',
          features: ['无限存储', 'Google服务', '教育优惠'],
          availability: 'in_stock',
          popularity: 95,
          image: '/images/gmail-edu.png'
        },
        {
          id: 2,
          name: 'Outlook 教育邮箱',
          type: 'outlook',
          category: 'education',
          price: 12.99,
          description: '微软Outlook教育邮箱，包含Office 365教育版',
          features: ['Office 365', '1TB OneDrive', '教育许可'],
          availability: 'in_stock',
          popularity: 88,
          image: '/images/outlook-edu.png'
        },
        {
          id: 3,
          name: 'Yahoo 教育邮箱',
          type: 'yahoo',
          category: 'education',
          price: 9.99,
          description: '经典Yahoo教育邮箱，稳定可靠',
          features: ['大容量存储', '稳定服务', '教育认证'],
          availability: 'limited',
          popularity: 72,
          image: '/images/yahoo-edu.png'
        },
        {
          id: 4,
          name: 'Gmail 企业邮箱',
          type: 'gmail',
          category: 'business',
          price: 25.99,
          description: 'Google Workspace企业邮箱，专业商务解决方案',
          features: ['自定义域名', '企业管理', '高级安全'],
          availability: 'in_stock',
          popularity: 91,
          image: '/images/gmail-business.png'
        },
        {
          id: 5,
          name: 'Outlook 企业邮箱',
          type: 'outlook',
          category: 'business',
          price: 22.99,
          description: 'Microsoft 365企业邮箱，完整办公套件',
          features: ['Office套件', '企业级安全', 'Teams集成'],
          availability: 'in_stock',
          popularity: 89,
          image: '/images/outlook-business.png'
        }
      ]
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800))
      
      products.value = mockProducts
      
      return { success: true, products: mockProducts }
    } catch (err) {
      error.value = err.message || '获取产品列表失败'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const fetchCategories = async () => {
    try {
      // TODO: Replace with actual API call
      // const response = await productsAPI.getCategories()
      
      // Mock categories for development
      const mockCategories = [
        { id: 'education', name: '教育邮箱', description: '适合学生和教育工作者' },
        { id: 'business', name: '企业邮箱', description: '专业商务邮箱解决方案' },
        { id: 'personal', name: '个人邮箱', description: '个人使用的高级邮箱' }
      ]
      
      categories.value = mockCategories
      return { success: true, categories: mockCategories }
    } catch (err) {
      error.value = err.message || '获取分类失败'
      return { success: false, error: error.value }
    }
  }

  const getProductById = (id) => {
    return products.value.find(product => product.id === parseInt(id))
  }

  const updateFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const resetFilters = () => {
    filters.value = {
      category: '',
      priceRange: [0, 1000],
      searchQuery: '',
      sortBy: 'name'
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Initialize products and categories
  const initializeProducts = async () => {
    await Promise.all([
      fetchProducts(),
      fetchCategories()
    ])
  }

  return {
    // State
    products,
    categories,
    isLoading,
    error,
    filters,
    
    // Getters
    filteredProducts,
    productsByCategory,
    
    // Actions
    fetchProducts,
    fetchCategories,
    getProductById,
    updateFilters,
    resetFilters,
    clearError,
    initializeProducts
  }
})
