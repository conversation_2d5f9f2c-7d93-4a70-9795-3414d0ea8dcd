{"version": 3, "names": ["_shallowEqual", "require", "_isType", "_isPlaceholderType", "_index", "is", "type", "node", "opts", "matches", "isType", "FLIPPED_ALIAS_KEYS", "isPlaceholderType", "expectedNode", "undefined", "shallowEqual"], "sources": ["../../src/validators/is.ts"], "sourcesContent": ["import shallowEqual from \"../utils/shallowEqual.ts\";\nimport isType from \"./isType.ts\";\nimport isPlaceholderType from \"./isPlaceholderType.ts\";\nimport { FLIPPED_ALIAS_KEYS } from \"../definitions/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function is<T extends t.Node[\"type\"]>(\n  type: T,\n  node: t.Node | null | undefined,\n  opts?: undefined,\n): node is Extract<t.Node, { type: T }>;\n\nexport default function is<\n  T extends t.Node[\"type\"],\n  P extends Extract<t.Node, { type: T }>,\n>(type: T, n: t.Node | null | undefined, required: Partial<P>): n is P;\n\nexport default function is<P extends t.Node>(\n  type: string,\n  node: t.Node | null | undefined,\n  opts: Partial<P>,\n): node is P;\n\nexport default function is(\n  type: string,\n  node: t.Node | null | undefined,\n  opts?: Partial<t.Node>,\n): node is t.Node;\n/**\n * Returns whether `node` is of given `type`.\n *\n * For better performance, use this instead of `is[Type]` when `type` is unknown.\n */\nexport default function is(\n  type: string,\n  node: t.Node | null | undefined,\n  opts?: Partial<t.Node>,\n): node is t.Node {\n  if (!node) return false;\n\n  const matches = isType(node.type, type);\n  if (!matches) {\n    if (!opts && node.type === \"Placeholder\" && type in FLIPPED_ALIAS_KEYS) {\n      // We can only return true if the placeholder doesn't replace a real node,\n      // but it replaces a category of nodes (an alias).\n      //\n      // t.is(\"Identifier\", node) gives some guarantees about node's shape, so we\n      // can't say that Placeholder(expectedNode: \"Identifier\") is an identifier\n      // because it doesn't have the same properties.\n      // On the other hand, t.is(\"Expression\", node) doesn't say anything about\n      // the shape of node because Expression can be many different nodes: we can,\n      // and should, safely report expression placeholders as Expressions.\n      return isPlaceholderType(node.expectedNode, type);\n    }\n    return false;\n  }\n\n  if (opts === undefined) {\n    return true;\n  } else {\n    return shallowEqual(node, opts);\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,kBAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AA8Be,SAASI,EAAEA,CACxBC,IAAY,EACZC,IAA+B,EAC/BC,IAAsB,EACN;EAChB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,OAAO,GAAG,IAAAC,eAAM,EAACH,IAAI,CAACD,IAAI,EAAEA,IAAI,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,IAAI,CAACD,IAAI,IAAID,IAAI,CAACD,IAAI,KAAK,aAAa,IAAIA,IAAI,IAAIK,yBAAkB,EAAE;MAUtE,OAAO,IAAAC,0BAAiB,EAACL,IAAI,CAACM,YAAY,EAAEP,IAAI,CAAC;IACnD;IACA,OAAO,KAAK;EACd;EAEA,IAAIE,IAAI,KAAKM,SAAS,EAAE;IACtB,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAO,IAAAC,qBAAY,EAACR,IAAI,EAAEC,IAAI,CAAC;EACjC;AACF", "ignoreList": []}