{"name": "@vitejs/plugin-vue", "version": "6.0.0", "type": "module", "license": "MIT", "author": "<PERSON>", "description": "The official plugin for Vue SFC support in Vite.", "keywords": ["vite", "vite-plugin", "vue"], "files": ["dist"], "exports": {".": "./dist/index.mjs", "./package.json": "./package.json"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite-plugin-vue.git", "directory": "packages/plugin-vue"}, "bugs": {"url": "https://github.com/vitejs/vite-plugin-vue/issues"}, "homepage": "https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#readme", "peerDependencies": {"vite": "^5.0.0 || ^6.0.0 || ^7.0.0", "vue": "^3.2.25"}, "devDependencies": {"@jridgewell/gen-mapping": "^0.3.8", "@jridgewell/trace-mapping": "^0.3.25", "debug": "^4.4.1", "rollup": "^4.43.0", "slash": "^5.1.0", "source-map-js": "^1.2.1", "vite": "^6.3.5", "vue": "^3.5.17"}, "dependencies": {"@rolldown/pluginutils": "1.0.0-beta.19"}, "scripts": {"dev": "unbuild --stub", "build": "unbuild"}}