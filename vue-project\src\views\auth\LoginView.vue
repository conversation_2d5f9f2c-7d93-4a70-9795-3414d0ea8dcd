<template>
  <div class="auth-page">
    <div class="auth-container">
      <!-- Auth Window -->
      <div class="auth-window">
        <!-- Window Header -->
        <div class="window-header">
          <div class="window-title">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/>
              <polyline points="10,17 15,12 10,7"/>
              <line x1="15" y1="12" x2="3" y2="12"/>
            </svg>
            <span>用户登录</span>
          </div>
          <div class="window-controls">
            <div class="window-control minimize"></div>
            <div class="window-control maximize"></div>
            <div class="window-control close"></div>
          </div>
        </div>

        <!-- Window Content -->
        <div class="window-content">
          <div class="auth-content-grid">
            <!-- Left Side - Logo and Info -->
            <div class="auth-info-section">
              <div class="auth-logo">
                <div class="logo-icon">
                  <svg width="64" height="64" viewBox="0 0 32 32" fill="none">
                    <rect x="2" y="6" width="28" height="20" rx="2" stroke="currentColor" stroke-width="2"/>
                    <path d="M2 8L16 18L30 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                </div>
                <h1 class="auth-title">邮箱售卖平台</h1>
                <p class="auth-subtitle">专业的邮箱服务提供商</p>
              </div>

              <div class="auth-features">
                <div class="feature-item">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                  </svg>
                  <span>安全可靠的邮箱服务</span>
                </div>
                <div class="feature-item">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <polyline points="12,6 12,12 16,14"/>
                  </svg>
                  <span>即时交付，无需等待</span>
                </div>
                <div class="feature-item">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                  </svg>
                  <span>24/7 专业客服支持</span>
                </div>
              </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="auth-form-section">
              <div class="form-header">
                <h2>登录您的账户</h2>
                <p>欢迎回来！请输入您的登录信息</p>
              </div>

              <!-- Login Form -->
              <form @submit.prevent="handleLogin" class="auth-form">
            <!-- Email Field -->
            <div class="form-group">
              <label for="email" class="form-label">邮箱地址</label>
              <input
                id="email"
                type="email"
                class="input"
                :class="{ 'input-error': errors.email }"
                v-model="form.email"
                placeholder="请输入您的邮箱地址"
                required
                autocomplete="email"
              >
              <div v-if="errors.email" class="error-message">{{ errors.email }}</div>
            </div>

            <!-- Password Field -->
            <div class="form-group">
              <label for="password" class="form-label">密码</label>
              <div class="password-input-wrapper">
                <input
                  id="password"
                  :type="showPassword ? 'text' : 'password'"
                  class="input"
                  :class="{ 'input-error': errors.password }"
                  v-model="form.password"
                  placeholder="请输入您的密码"
                  required
                  autocomplete="current-password"
                >
                <button
                  type="button"
                  class="password-toggle"
                  @click="togglePassword"
                  :aria-label="showPassword ? '隐藏密码' : '显示密码'"
                >
                  <svg v-if="showPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                  </svg>
                  <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                  </svg>
                </button>
              </div>
              <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="form-options">
              <label class="checkbox-wrapper">
                <input type="checkbox" v-model="form.rememberMe" class="checkbox">
                <span class="checkbox-label">记住我</span>
              </label>
              <a href="#" class="forgot-password">忘记密码？</a>
            </div>

            <!-- Error Message -->
            <div v-if="authError" class="error-banner">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="15" y1="9" x2="9" y2="15"/>
                <line x1="9" y1="9" x2="15" y2="15"/>
              </svg>
              <span>{{ authError }}</span>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn btn-primary btn-full" :disabled="isLoading">
              <svg v-if="isLoading" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="loading-spinner">
                <path d="M21 12a9 9 0 1 1-6.219-8.56"/>
              </svg>
              <span>{{ isLoading ? '登录中...' : '登录' }}</span>
            </button>
          </form>

          <!-- Divider -->
          <div class="auth-divider">
            <span>或</span>
          </div>

          <!-- Social Login -->
          <div class="social-login">
            <button class="btn btn-outline social-btn" @click="handleSocialLogin('google')">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>Google 登录</span>
            </button>
            <button class="btn btn-outline social-btn" @click="handleSocialLogin('github')">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              <span>GitHub 登录</span>
            </button>
          </div>

          <!-- Register Link -->
          <div class="auth-footer">
            <p>还没有账户？ <router-link to="/register" class="auth-link">立即注册</router-link></p>
          </div>
        </div>
      </div>
    </div>

      <!-- Decorative Graphics -->
      <div class="decorative-graphics">
        <div class="graphic-grid">
          <div class="grid-dot"></div>
          <div class="grid-dot"></div>
          <div class="grid-dot"></div>
          <div class="grid-dot"></div>
          <div class="grid-dot"></div>
          <div class="grid-dot"></div>
          <div class="grid-dot"></div>
          <div class="grid-dot"></div>
          <div class="grid-dot"></div>
        </div>
        <div class="graphic-wireframe">
          <svg width="200" height="150" viewBox="0 0 200 150" fill="none">
            <rect x="10" y="10" width="180" height="130" rx="8" stroke="currentColor" stroke-width="2" stroke-dasharray="5,5"/>
            <rect x="30" y="30" width="140" height="20" rx="4" stroke="currentColor" stroke-width="1"/>
            <rect x="30" y="60" width="100" height="15" rx="4" stroke="currentColor" stroke-width="1"/>
            <rect x="30" y="85" width="120" height="15" rx="4" stroke="currentColor" stroke-width="1"/>
            <circle cx="160" cy="70" r="15" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Reactive state
const form = ref({
  email: '',
  password: '',
  rememberMe: false
})

const errors = ref({})
const showPassword = ref(false)

// Computed properties
const isLoading = computed(() => authStore.isLoading)
const authError = computed(() => authStore.error)

// Methods
const validateForm = () => {
  errors.value = {}
  
  if (!form.value.email) {
    errors.value.email = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
    errors.value.email = '请输入有效的邮箱地址'
  }
  
  if (!form.value.password) {
    errors.value.password = '请输入密码'
  } else if (form.value.password.length < 6) {
    errors.value.password = '密码至少需要6个字符'
  }
  
  return Object.keys(errors.value).length === 0
}

const handleLogin = async () => {
  if (!validateForm()) return
  
  authStore.clearError()
  
  const result = await authStore.login({
    email: form.value.email,
    password: form.value.password,
    rememberMe: form.value.rememberMe
  })
  
  if (result.success) {
    // Redirect to intended page or dashboard
    const redirectTo = route.query.redirect || '/dashboard'
    router.push(redirectTo)
  }
}

const handleSocialLogin = (provider) => {
  // TODO: Implement social login
  console.log(`Social login with ${provider}`)
  // This would typically redirect to OAuth provider
}

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// Lifecycle
onMounted(() => {
  // Clear any existing errors
  authStore.clearError()
})
</script>

<style scoped>
/* Login page specific styles - common auth styles are in main.css */

.window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-secondary-surface);
  border-bottom: var(--border-width) solid var(--color-neutral-black);
}

.window-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  font-size: 0.875rem;
  text-transform: uppercase;
}

.auth-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  min-height: 500px;
}

.auth-info-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: var(--spacing-xl);
  background-color: var(--color-secondary-surface);
  border-right: var(--border-width) solid var(--color-neutral-black);
}

.auth-form-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: var(--spacing-xl);
}

.form-header {
  margin-bottom: var(--spacing-xl);
}

.form-header h2 {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
}

.form-header p {
  color: #666;
  font-size: 0.875rem;
}

.auth-features {
  margin-top: var(--spacing-2xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
}

.feature-item svg {
  flex-shrink: 0;
  color: var(--color-neutral-black);
}

.btn-full {
  width: 100%;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .auth-content-grid {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .auth-info-section {
    border-right: none;
    border-bottom: var(--border-width) solid var(--color-neutral-black);
    padding: var(--spacing-lg);
  }

  .auth-form-section {
    padding: var(--spacing-lg);
  }

  .auth-features {
    margin-top: var(--spacing-lg);
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }

  .feature-item {
    flex: 1;
    min-width: 200px;
    font-size: 0.75rem;
  }
}

@media (max-width: 768px) {
  .auth-features {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .feature-item {
    min-width: auto;
  }
}
</style>
